import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { GuestCartService, GuestCart, GuestCartItem } from '../../core/services/guest-cart.service';
import { AuthService } from '../../core/services/auth.service';
import { CartService } from '../../core/services/cart.service';
import { EnhancedRegistrationService } from '../../core/services/enhanced-registration.service';
import { ModalService } from '../../core/services/modal.service';

@Component({
  selector: 'app-guest-cart',
  standalone: false,
  templateUrl: './guest-cart.component.html',
  styleUrl: './guest-cart.component.css'
})
export class GuestCartComponent implements OnInit, OnDestroy {
  cart: GuestCart = { items: [], totalItems: 0, totalPrice: 0 };
  loading = false;
  error = '';

  // Registration form
  registrationForm: FormGroup;
  verificationForm: FormGroup;
  showCodeField = false;
  registeredEmail = '';
  registrationPassword = '';
  registrationLoading = false;
  verificationLoading = false;
  registrationError = '';
  fieldErrors: any = {};
  isProcessingRegistration = false;

  private cartSubscription?: Subscription;

  constructor(
    private guestCartService: GuestCartService,
    private authService: AuthService,
    private cartService: CartService,
    private enhancedRegistrationService: EnhancedRegistrationService,
    private modalService: ModalService,
    private router: Router,
    private fb: FormBuilder
  ) {
    this.registrationForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      password_confirm: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });

    this.verificationForm = this.fb.group({
      code: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });
  }

  ngOnInit(): void {
    this.loadCart();
    this.setupCartSubscription();
  }

  ngOnDestroy(): void {
    this.cartSubscription?.unsubscribe();
  }

  private setupCartSubscription(): void {
    this.cartSubscription = this.guestCartService.cart$.subscribe(cart => {
      this.cart = cart;
    });
  }

  private passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('password_confirm');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  loadCart(): void {
    this.loading = true;
    this.error = '';

    this.guestCartService.getCart().subscribe({
      next: (cart) => {
        this.cart = cart;
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить корзину';
        this.loading = false;
      }
    });
  }

  // Helper methods to get item data
  getItemTitle(item: GuestCartItem): string {
    if (item.type === 'game' && item.game) {
      return item.game.title;
    } else if (item.type === 'package' && item.gamePackage) {
      return item.gamePackage.name;
    }
    return 'Unknown Item';
  }

  getItemPrice(item: GuestCartItem): string {
    if (item.type === 'game' && item.game) {
      return item.game.price;
    } else if (item.type === 'package' && item.gamePackage) {
      return item.gamePackage.price;
    }
    return '0';
  }

  getItemImage(item: GuestCartItem): string | null {
    if (item.type === 'game' && item.game) {
      return item.game.cover_image;
    }
    // Game packages don't have cover images
    return null;
  }

  getItemDescription(item: GuestCartItem): string {
    if (item.type === 'game' && item.game) {
      return item.game.description;
    } else if (item.type === 'package' && item.gamePackage) {
      return item.gamePackage.description;
    }
    return '';
  }

  isGameItem(item: GuestCartItem): boolean {
    return item.type === 'game';
  }

  isPackageItem(item: GuestCartItem): boolean {
    return item.type === 'package';
  }

  removeItem(item: GuestCartItem): void {
    const itemTitle = this.getItemTitle(item);
    this.modalService.confirm(
      'Удалить из корзины',
      `Вы уверены, что хотите удалить "${itemTitle}" из корзины?`
    ).then((confirmed) => {
      if (confirmed) {
        this.guestCartService.removeItem(item.id).subscribe({
          next: () => {
            console.log('Item removed successfully');
          },
          error: (error) => {
            console.error('Error removing item:', error);
            this.modalService.error('Ошибка', 'Не удалось удалить товар: ' + error.message);
          }
        });
      }
    });
  }

  clearCart(): void {
    if (this.cart.items.length === 0) return;

    this.modalService.confirm(
      'Очистить корзину',
      'Вы уверены, что хотите удалить все товары из корзины?'
    ).then((confirmed) => {
      if (confirmed) {
        this.guestCartService.clearCart().subscribe({
          next: () => {
            console.log('Cart cleared successfully');
          },
          error: (error) => {
            console.error('Error clearing cart:', error);
            this.modalService.error('Ошибка', 'Не удалось очистить корзину: ' + error.message);
          }
        });
      }
    });
  }

  goToGames(): void {
    this.router.navigate(['/']).then(() => {
      setTimeout(() => {
        const element = document.getElementById('games');
        if (element) {
          const headerHeight = 80;
          const elementPosition = element.offsetTop - headerHeight;
          window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
          });
        }
      }, 100);
    });
  }

  viewGameDetails(gameId: number | undefined): void {
    if (gameId) {
      this.router.navigate(['/games', gameId]);
    }
  }

  // Registration methods
  onRegister(): void {
    if (this.registrationForm.valid) {
      this.registrationError = '';
      this.clearFieldErrors();
      this.registrationLoading = true;

      const credentials = this.registrationForm.value;
      this.registrationPassword = credentials.password; // Store for auto-login

      this.authService.register(credentials).subscribe({
        next: (response) => {
          this.registeredEmail = response.email;
          this.showCodeField = true;
          this.registrationLoading = false;
          this.modalService.success('Регистрация', 'Код подтверждения отправлен на ваш email');
        },
        error: (error) => {
          this.registrationLoading = false;
          this.handleRegistrationError(error);
        }
      });
    }
  }

  onVerifyCode(): void {
    if (this.verificationForm.valid) {
      this.registrationError = '';
      this.isProcessingRegistration = true;
      this.verificationLoading = true;

      const registrationData = {
        email: this.registeredEmail,
        password: this.registrationPassword
      };

      const verificationCode = this.verificationForm.value.code;

      this.enhancedRegistrationService.registerAndLogin(registrationData, verificationCode).subscribe({
        next: (result) => {
          this.isProcessingRegistration = false;
          this.verificationLoading = false;

          let message = result.message;
          if (result.cartTransferred && result.transferredItemsCount > 0) {
            message += ` Перенесено товаров: ${result.transferredItemsCount}.`;
          }

          this.modalService.success('Добро пожаловать!', message).then(() => {
            // Navigate to authenticated cart
            this.router.navigate(['/profile/cart']);
          });
        },
        error: (error) => {
          this.isProcessingRegistration = false;
          this.verificationLoading = false;
          this.handleVerificationError(error);
        }
      });
    }
  }

  private handleRegistrationError(error: any): void {
    if (error.error) {
      // Handle field-specific errors
      this.fieldErrors = error.error;
      
      // Handle non-field errors
      if (error.error.non_field_errors) {
        this.registrationError = error.error.non_field_errors.join(', ');
      }
    } else {
      this.registrationError = error.message || 'Произошла ошибка при регистрации';
    }
  }

  private handleVerificationError(error: any): void {
    if (error.error && error.error.non_field_errors) {
      this.registrationError = error.error.non_field_errors.join(', ');
    } else {
      this.registrationError = error.message || 'Произошла ошибка при верификации';
    }
  }

  private clearFieldErrors(): void {
    this.fieldErrors = {};
  }

  hasFieldError(fieldName: string): boolean {
    return this.fieldErrors[fieldName] && this.fieldErrors[fieldName].length > 0;
  }

  getFieldError(fieldName: string): string {
    return this.hasFieldError(fieldName) ? this.fieldErrors[fieldName][0] : '';
  }
}
