import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { CartService } from '../../core/services/cart.service';
import { CheckoutService } from '../../core/services/checkout.service';
import { UserSummaryService } from '../../core/services/user-summary.service';
import { ModalService } from '../../core/services/modal.service';
import { AuthService } from '../../core/services/auth.service';
import { GuestCartService, GuestCart, GuestCartItem } from '../../core/services/guest-cart.service';
import { Cart, CartItem, Purchase } from '../../core/models/cart.model';

@Component({
  selector: 'app-cart',
  standalone: false,
  templateUrl: './cart.component.html',
  styleUrl: './cart.component.css'
})
export class CartComponent implements OnInit, OnDestroy {
  cart: Cart = { items: [], total_items: 0, total_price: 0 };
  guestCart: GuestCart = { items: [], totalItems: 0, totalPrice: 0 };
  loading = false;
  error = '';
  checkoutLoading = false;

  private cartSubscription?: Subscription;
  private guestCartSubscription?: Subscription;

  constructor(
    private cartService: CartService,
    private checkoutService: CheckoutService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService,
    private authService: AuthService,
    private guestCartService: GuestCartService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadCart();
    this.setupCartSubscription();
  }

  ngOnDestroy(): void {
    this.cartSubscription?.unsubscribe();
    this.guestCartSubscription?.unsubscribe();
  }

  private setupCartSubscription(): void {
    if (this.authService.isAuthenticated()) {
      this.cartSubscription = this.cartService.cart$.subscribe(cart => {
        this.cart = cart;
      });
    } else {
      this.guestCartSubscription = this.guestCartService.cart$.subscribe(cart => {
        this.guestCart = cart;
      });
    }
  }

  loadCart(): void {
    this.loading = true;
    this.error = '';

    if (this.authService.isAuthenticated()) {
      this.cartService.loadCart().subscribe({
        next: (cart) => {
          this.cart = cart;
          this.loading = false;
        },
        error: (error) => {
          this.error = error.message || 'Не удалось загрузить корзину';
          this.loading = false;
        }
      });
    } else {
      // For guest users, cart is already loaded from localStorage via subscription
      this.loading = false;
    }
  }

  // Unified methods to work with both authenticated and guest carts
  getTotalItems(): number {
    return this.authService.isAuthenticated() ? this.cart.total_items : this.guestCart.totalItems;
  }

  getTotalPrice(): string | number {
    return this.authService.isAuthenticated() ? this.cart.total_price : this.guestCart.totalPrice;
  }

  getCartItems(): (CartItem | GuestCartItem)[] {
    return this.authService.isAuthenticated() ? this.cart.items : this.guestCart.items;
  }

  // Helper methods to get item data - work with both CartItem and GuestCartItem
  getItemTitle(item: CartItem | GuestCartItem): string {
    if (this.isCartItem(item)) {
      const cartItem = item as CartItem;
      if (cartItem.game_obj) {
        return cartItem.game_obj.title;
      } else if (cartItem.game_package_obj) {
        return cartItem.game_package_obj.name;
      }
    } else {
      const guestItem = item as GuestCartItem;
      if (guestItem.game) {
        return guestItem.game.title;
      } else if (guestItem.gamePackage) {
        return guestItem.gamePackage.name;
      }
    }
    return 'Unknown Item';
  }

  getItemPrice(item: CartItem | GuestCartItem): string {
    if (this.isCartItem(item)) {
      const cartItem = item as CartItem;
      if (cartItem.game_obj) {
        return cartItem.game_obj.price;
      } else if (cartItem.game_package_obj) {
        return cartItem.game_package_obj.price;
      }
    } else {
      const guestItem = item as GuestCartItem;
      if (guestItem.game) {
        return guestItem.game.price;
      } else if (guestItem.gamePackage) {
        return guestItem.gamePackage.price;
      }
    }
    return '0';
  }

  getItemImage(item: CartItem | GuestCartItem): string | null {
    if (this.isCartItem(item)) {
      const cartItem = item as CartItem;
      if (cartItem.game_obj) {
        return cartItem.game_obj.cover_image;
      }
    } else {
      const guestItem = item as GuestCartItem;
      if (guestItem.game) {
        return guestItem.game.cover_image;
      }
    }
    // Game packages don't have cover images
    return null;
  }

  getItemDescription(item: CartItem | GuestCartItem): string {
    if (this.isCartItem(item)) {
      const cartItem = item as CartItem;
      if (cartItem.game_obj) {
        return cartItem.game_obj.description;
      } else if (cartItem.game_package_obj) {
        return cartItem.game_package_obj.description;
      }
    } else {
      const guestItem = item as GuestCartItem;
      if (guestItem.game) {
        return guestItem.game.description;
      } else if (guestItem.gamePackage) {
        return guestItem.gamePackage.description;
      }
    }
    return '';
  }

  isGameItem(item: CartItem | GuestCartItem): boolean {
    if (this.isCartItem(item)) {
      return !!(item as CartItem).game_obj;
    } else {
      return !!(item as GuestCartItem).game;
    }
  }

  isPackageItem(item: CartItem | GuestCartItem): boolean {
    if (this.isCartItem(item)) {
      return !!(item as CartItem).game_package_obj;
    } else {
      return !!(item as GuestCartItem).gamePackage;
    }
  }

  // Type guard to distinguish between CartItem and GuestCartItem
  private isCartItem(item: CartItem | GuestCartItem): item is CartItem {
    return this.authService.isAuthenticated();
  }

  // Additional helper methods for the template
  getGameId(item: CartItem | GuestCartItem): number | undefined {
    if (this.isCartItem(item)) {
      return (item as CartItem).game_obj?.id;
    } else {
      return (item as GuestCartItem).game?.id;
    }
  }

  getPackageGames(item: CartItem | GuestCartItem): any[] | undefined {
    if (this.isCartItem(item)) {
      return (item as CartItem).game_package_obj?.games;
    } else {
      return (item as GuestCartItem).gamePackage?.games;
    }
  }

  getPackageDuration(item: CartItem | GuestCartItem): number | undefined {
    if (this.isCartItem(item)) {
      return (item as CartItem).game_package_obj?.duration_days;
    } else {
      return (item as GuestCartItem).gamePackage?.duration_days;
    }
  }

  getItemType(item: CartItem | GuestCartItem): string {
    return this.isGameItem(item) ? 'Игра' : 'Пакет';
  }



  removeItem(item: CartItem | GuestCartItem): void {
    const itemTitle = this.getItemTitle(item);
    const itemType = this.isGameItem(item) ? 'игру' : 'пакет';
    this.modalService.confirm(
      'Удаление из корзины',
      `Вы уверены, что хотите удалить "${itemTitle}" из корзины?`,
      'Удалить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        if (this.authService.isAuthenticated()) {
          const cartItem = item as CartItem;
          this.cartService.removeFromCart(cartItem.id).subscribe({
            next: () => {
              console.log('Item removed successfully');
            },
            error: (error) => {
              console.error('Error removing item:', error.message);
              this.modalService.error('Ошибка', `Не удалось удалить ${itemType} из корзины: ` + error.message);
            }
          });
        } else {
          const guestItem = item as GuestCartItem;
          this.guestCartService.removeItem(guestItem.id).subscribe({
            next: () => {
              console.log('Guest item removed successfully');
            },
            error: (error) => {
              console.error('Error removing guest item:', error.message);
              this.modalService.error('Ошибка', `Не удалось удалить ${itemType} из корзины: ` + error.message);
            }
          });
        }
      }
    });
  }

  clearCart(): void {
    this.modalService.confirm(
      'Очистка корзины',
      'Вы уверены, что хотите удалить все игры из корзины?',
      'Очистить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        if (this.authService.isAuthenticated()) {
          this.cartService.clearCart().subscribe({
            next: () => {
              console.log('Cart cleared successfully');
            },
            error: (error) => {
              console.error('Error clearing cart:', error.message);
              this.modalService.error('Ошибка', 'Не удалось очистить корзину: ' + error.message);
            }
          });
        } else {
          this.guestCartService.clearCart().subscribe({
            next: () => {
              console.log('Guest cart cleared successfully');
            },
            error: (error) => {
              console.error('Error clearing guest cart:', error.message);
              this.modalService.error('Ошибка', 'Не удалось очистить корзину: ' + error.message);
            }
          });
        }
      }
    });
  }



  checkout(): void {
    if (this.getCartItems().length === 0) {
      this.modalService.error('Ошибка', 'Корзина пуста');
      return;
    }

    if (!this.authService.isAuthenticated()) {
      this.modalService.error('Требуется авторизация', 'Для оформления заказа необходимо войти в систему или зарегистрироваться.');
      this.router.navigate(['/login']);
      return;
    }

    const totalItems = this.getTotalItems();
    this.modalService.confirm(
      'Подтверждение покупки',
      `Вы уверены, что хотите купить ${totalItems} ${totalItems === 1 ? 'игру' : totalItems < 5 ? 'игры' : 'игр'} на сумму ${this.formatPrice(this.getTotalPrice())}?`,
      'Купить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.processCheckout();
      }
    });
  }

  private processCheckout(): void {
    this.checkoutLoading = true;

    this.checkoutService.checkout().subscribe({
      next: (purchases: Purchase[]) => {
        this.checkoutLoading = false;

        // Refresh user summary to update cart count (cart should be cleared after checkout)
        this.userSummaryService.refreshSummary();

        // Show success message
        const purchaseCount = purchases.length;
        const successMessage = `Успешно приобретено ${purchaseCount} ${purchaseCount === 1 ? 'игра' : purchaseCount < 5 ? 'игры' : 'игр'}!`;

        this.modalService.success('Покупки созданы', successMessage + ' Сейчас вы будете перенаправлены на страницу оплаты.').then(() => {
          // Navigate directly to purchase history for payment
          this.router.navigate(['/profile/purchases']);
        });
      },
      error: (error) => {
        this.checkoutLoading = false;
        console.error('Checkout error:', error.message);
        this.modalService.error('Ошибка покупки', 'Не удалось завершить покупку: ' + error.message);
      }
    });
  }

  formatPrice(price: string | number): string {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  goToGames(): void {
    // Navigate to main page games section instead of profile games catalog
    this.router.navigate(['/']).then(() => {
      setTimeout(() => {
        const element = document.getElementById('games');
        if (element) {
          const headerHeight = 80;
          const elementPosition = element.offsetTop - headerHeight;
          window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
          });
        }
      }, 100);
    });
  }

  viewGameDetails(gameId: number | undefined): void {
    if (gameId) {
      this.router.navigate(['/games', gameId]);
    }
  }
}
