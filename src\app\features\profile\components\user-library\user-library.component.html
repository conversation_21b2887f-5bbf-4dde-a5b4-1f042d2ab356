<!-- User Library Component -->
<div class="max-w-6xl space-y-4 lg:space-y-6">
  <!-- Header -->
  <div class="mb-4 lg:mb-6">
    <h2 class="text-xl lg:text-2xl font-bold text-white mb-2">Моя библиотека</h2>
    <p class="text-sm lg:text-base text-gray-400">Ваши приобретенные игры</p>
  </div>

  <!-- Search and Filter Controls -->
  <div class="bg-slate-800/40 backdrop-blur-sm border border-slate-600/50 rounded-lg p-3 lg:p-4">
    <div class="flex flex-col sm:flex-row gap-3 lg:gap-4 items-stretch sm:items-center justify-between">
      
      <!-- Search Input -->
      <div class="flex-1 max-w-full sm:max-w-md">
        <div class="relative">
          <input
            type="text"
            [(ngModel)]="searchTerm"
            (input)="onSearchChange()"
            placeholder="Поиск игр в библиотеке..."
            class="w-full bg-slate-700/50 border border-slate-600/50 rounded-lg px-3 lg:px-4 py-2 lg:py-2.5 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm lg:text-base"
          >
          <svg class="absolute right-3 top-2.5 lg:top-3 h-4 w-4 lg:h-5 lg:w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
      </div>

      <!-- Sort Dropdown -->
      <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
        <label class="text-gray-300 text-xs lg:text-sm">Сортировка:</label>
        <select
          [(ngModel)]="sortBy"
          (change)="onSortChange()"
          class="bg-slate-700/50 border border-slate-600/50 rounded-lg px-3 py-2 lg:py-2.5 text-white text-sm lg:text-base focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[44px] sm:min-h-auto"
        >
          <option value="-added_at">Недавно добавленные</option>
          <option value="added_at">Давно добавленные</option>
          <option value="game__title">По названию (А-Я)</option>
          <option value="-game__title">По названию (Я-А)</option>
          <option value="game__price">По цене (возрастание)</option>
          <option value="-game__price">По цене (убывание)</option>
        </select>
      </div>

      <!-- Results Count -->
      <div class="text-gray-300 text-sm">
        Найдено: {{ totalItems }} {{ totalItems === 1 ? 'игра' : totalItems < 5 ? 'игры' : 'игр' }}
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4">
    <div class="flex items-center">
      <svg class="w-5 h-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <div>
        <h3 class="text-red-400 font-medium text-sm">Ошибка загрузки</h3>
        <p class="text-red-300 text-sm">{{ error }}</p>
      </div>
    </div>
    <button
      (click)="loadLibrary()"
      class="mt-3 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && !error && libraryItems.length === 0" class="text-center py-8">
    <div class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
      <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
      </svg>
      <h3 class="text-lg font-medium text-white mb-2">Ваша библиотека пуста</h3>
      <p class="text-gray-400 mb-4">У вас пока нет приобретенных игр</p>
      <a
        href="/games"
        class="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 font-medium text-sm"
      >
        Перейти к каталогу игр
      </a>
    </div>
  </div>

  <!-- Library Grid -->
  <div *ngIf="!loading && !error && libraryItems.length > 0" class="space-y-4">
    <!-- Games Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 lg:gap-4">
      <div
        *ngFor="let item of libraryItems"
        class="bg-slate-800/40 border border-slate-600/50 rounded-lg overflow-hidden hover:border-slate-500/60 transition-all duration-300 hover:transform hover:scale-[1.02] cursor-pointer group"
        (click)="goToGameDetail(item.game.id)"
      >
        <!-- Game Cover Image -->
        <div class="h-28 sm:h-32 lg:h-36 bg-gradient-to-br from-slate-700 to-slate-800 relative overflow-hidden">
          <img
            *ngIf="item.game.cover_image"
            [src]="item.game.cover_image"
            [alt]="item.game.title"
            class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
          >
          <div *ngIf="!item.game.cover_image" class="w-full h-full flex items-center justify-center">
            <svg class="w-12 h-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </div>
          
          <!-- Access Status Badge -->
          <div class="absolute top-2 right-2">
            <!-- Active Access -->
            <span
              *ngIf="hasActiveAccess(item.game)"
              class="bg-green-600 text-white text-xs px-2 py-1 rounded-full font-medium"
            >
              Играть
            </span>
            <!-- Unactivated Access -->
            <span
              *ngIf="!hasActiveAccess(item.game) && hasUnactivatedAccess(item.game)"
              class="bg-yellow-600 text-white text-xs px-2 py-1 rounded-full font-medium"
            >
              Активировать
            </span>
            <!-- Expired Access -->
            <span
              *ngIf="!hasActiveAccess(item.game) && !hasUnactivatedAccess(item.game)"
              class="bg-red-600 text-white text-xs px-2 py-1 rounded-full font-medium"
            >
              Истёк
            </span>
          </div>
        </div>

        <!-- Game Info -->
        <div class="p-3">
          <h3 class="text-white font-semibold text-sm mb-1 line-clamp-1">{{ item.game.title }}</h3>
          <p *ngIf="item.game.subtitle" class="text-gray-400 text-xs mb-1 line-clamp-1">{{ item.game.subtitle }}</p>
          <p class="text-gray-300 text-xs mb-2 line-clamp-2">{{ item.game.description }}</p>

          <!-- Access Information -->
          <div *ngIf="item.game.access_end || hasUnactivatedAccess(item.game)" class="mb-2 p-2 rounded-lg text-xs"
               [class]="hasActiveAccess(item.game) ? 'bg-blue-500/10 border border-blue-500/30' : hasUnactivatedAccess(item.game) ? 'bg-yellow-500/10 border border-yellow-500/30' : 'bg-red-500/10 border border-red-500/30'">
            <!-- Active Access -->
            <div *ngIf="hasActiveAccess(item.game)" class="space-y-1">
              <div class="flex items-center gap-1">
                <svg class="w-3 h-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-blue-400 font-medium">Доступ активен</span>
              </div>
              <p class="text-blue-300">Осталось: {{ getRemainingTime(item.game) }}</p>
            </div>
            <!-- Unactivated Access -->
            <div *ngIf="!hasActiveAccess(item.game) && hasUnactivatedAccess(item.game)" class="space-y-2">
              <div class="flex items-center gap-1">
                <svg class="w-3 h-3 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 2L13.09 8.26L20 9L15 13.74L16.18 20.02L10 16.77L3.82 20.02L5 13.74L0 9L6.91 8.26L10 2Z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-yellow-400 font-medium">Готов к активации</span>
              </div>
              <p class="text-yellow-300 text-xs">Доступ на 1 день готов к активации</p>
              <div class="mt-1 p-1 bg-yellow-500/10 border border-yellow-500/30 rounded">
                <p class="text-yellow-400 text-xs font-medium">Нажмите для активации</p>
              </div>
            </div>
            <!-- Expired Access -->
            <div *ngIf="!hasActiveAccess(item.game) && !hasUnactivatedAccess(item.game)" class="space-y-2">
              <div class="flex items-center gap-1">
                <svg class="w-3 h-3 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <span class="text-red-400 font-medium">Доступ истёк</span>
              </div>
              <p class="text-red-300 text-xs">{{ getAccessEndDate(item.game) }}</p>
              <div class="mt-1 p-1 bg-blue-500/10 border border-blue-500/30 rounded">
                <p class="text-blue-400 text-xs font-medium">Можете продлить</p>
                <button
                  (click)="extendAccess(item.game)"
                  class="text-blue-400 hover:text-blue-300 text-xs underline transition-colors"
                >
                  Продлить
                </button>
              </div>
            </div>
          </div>

          <!-- Download Section (only for users with active access) -->
          <div *ngIf="hasActiveAccess(item.game)" class="mb-2">
            <!-- <div class="flex items-center justify-between mb-2">
              <span class="text-gray-300 text-xs font-medium">Скачать игру:</span>
              <button
                (click)="loadGameFiles(item.game.id)"
                *ngIf="!gameFilesMap[item.game.id] && !loadingGameFiles[item.game.id]"
                class="text-blue-400 hover:text-blue-300 text-xs underline transition-colors">
                Показать файлы
              </button>
              <span *ngIf="loadingGameFiles[item.game.id]" class="text-gray-400 text-xs">Загрузка...</span>
            </div> -->

            <!-- Game Files List -->
            <div *ngIf="gameFilesMap[item.game.id]" class="space-y-1">
              <div *ngIf="!hasGameFiles(item.game.id)" class="text-gray-400 text-xs">
                Файлы для скачивания недоступны
              </div>
              <div *ngFor="let gameFile of getGameFiles(item.game.id)"
                   class="flex items-center justify-between p-2 bg-slate-700/30 rounded border border-slate-600/30">
                <div class="flex-1">
                  <div class="text-white text-xs font-medium">{{ gameFile.file_name }}</div>
                  <div class="text-gray-400 text-xs">
                    {{ getPlatformLabel(gameFile.platform) }} • v{{ gameFile.version }} • {{ formatFileSize(gameFile.file_size) }}
                  </div>
                </div>
                <button
                  (click)="downloadGameFile(gameFile)"
                  class="px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition-colors duration-200"
                  title="Скачать {{ gameFile.file_name }}">
                  ↓
                </button>
              </div>
            </div>
          </div>

          <!-- Added Date -->
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-400">{{ formatDate(item.added_at) }}</span>
            <span class="text-blue-400 font-medium">{{ item.game.price }}₽</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div *ngIf="getTotalPages() > 1" class="flex justify-center items-center space-x-2 mt-6">
      <!-- Previous Button -->
      <button
        (click)="previousPage()"
        [disabled]="!hasPrevious"
        class="px-3 py-2 rounded-lg bg-slate-700/50 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600/50 transition-colors text-sm"
      >
        ←
      </button>

      <!-- Page Numbers -->
      <button
        *ngFor="let page of getPageNumbers()"
        (click)="goToPage(page)"
        [class]="page === currentPage ? 
          'px-3 py-2 rounded-lg bg-blue-600 text-white text-sm' : 
          'px-3 py-2 rounded-lg bg-slate-700/50 text-white hover:bg-slate-600/50 transition-colors text-sm'"
      >
        {{ page }}
      </button>

      <!-- Next Button -->
      <button
        (click)="nextPage()"
        [disabled]="!hasNext"
        class="px-3 py-2 rounded-lg bg-slate-700/50 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600/50 transition-colors text-sm"
      >
        →
      </button>
    </div>
  </div>
</div>
