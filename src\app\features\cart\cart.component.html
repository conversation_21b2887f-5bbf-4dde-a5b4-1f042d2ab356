<!-- Cart Page -->
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-gray-900 pt-20">
  <!-- Main Content -->
  <div class="pt-8 pb-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

      <!-- Profile Cart Design -->
      <div class="max-w-6xl">
        <!-- Header -->
        <div class="mb-6">
          <h1 class="text-2xl font-bold text-white mb-2">Корзина</h1>
          <p class="text-gray-300">
            {{ getTotalItems() }}
            {{
              getTotalItems() === 1
                ? "товар"
                : getTotalItems() < 5
                ? "товара"
                : "товаров"
            }}
          </p>
        </div>

        <!-- Loading State -->
        <div *ngIf="loading" class="flex justify-center py-12">
          <div
            class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"
          ></div>
        </div>

        <!-- Error State -->
        <div *ngIf="!loading && error" class="text-center py-12">
          <div class="text-red-400 mb-4">{{ error }}</div>
          <button
            (click)="loadCart()"
            class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Попробовать снова
          </button>
        </div>

        <!-- Empty Cart -->
        <div
          *ngIf="!loading && !error && getCartItems().length === 0"
          class="text-center py-8"
        >
          <div class="text-gray-400 mb-4">
            <img
              src="assets/icons/cart.svg"
              width="50"
              alt="Empty Cart"
              class="mx-auto mb-3 opacity-40"
            />
            <p class="text-lg">Ваша корзина пуста</p>
          </div>
          <button
            (click)="goToGames()"
            class="px-4 text-sm py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors font-medium"
          >
            Перейти к играм
          </button>
        </div>

        <!-- Cart Content - Two Column Layout -->
        <div
          *ngIf="!loading && !error && getCartItems().length > 0"
          class="cart-grid grid grid-cols-1 lg:grid-cols-3 gap-6"
        >
          <!-- Left Column - Cart Items -->
          <div class="lg:col-span-2 space-y-4">
            <div
              *ngFor="let item of getCartItems()"
              class="cart-item bg-slate-800/50 border border-slate-600/50 rounded-lg p-4 hover:border-slate-500/60 transition-all"
            >
              <div class="relative">
                <!-- Remove Button - Top Right -->
                <button
                  (click)="removeItem(item)"
                  class="remove-button absolute top-3 right-3 p-2 text-gray-400 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all z-10"
                  title="Удалить из корзины"
                >
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    ></path>
                  </svg>
                </button>

                <!-- Item Image - Top Left -->
                <div
                  class="cart-item-image w-48 h-36 bg-slate-700 rounded-lg overflow-hidden mb-4"
                >
                  <img
                    *ngIf="getItemImage(item)"
                    [src]="getItemImage(item)!"
                    [alt]="getItemTitle(item)"
                    class="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform"
                    (click)="isGameItem(item) && viewGameDetails(getGameId(item))"
                  />
                  <div
                    *ngIf="!getItemImage(item)"
                    class="w-full h-full flex items-center justify-center text-gray-400 text-xs text-center"
                  >
                    <span *ngIf="isGameItem(item)">Нет изображения</span>
                    <span *ngIf="isPackageItem(item)">Пакет игр</span>
                  </div>
                </div>

                <!-- Item Details - Below Image -->
                <div class="cart-item-details">
                  <h3
                    class="text-lg font-bold text-white mb-2 cursor-pointer hover:text-blue-400 transition-colors"
                    (click)="isGameItem(item) && viewGameDetails(getGameId(item))"
                  >
                    {{ getItemTitle(item) }}
                  </h3>

                  <p class="text-gray-400 text-sm mb-3 line-clamp-2">
                    {{ getItemDescription(item) }}
                  </p>

                  <!-- Package games list -->
                  <div *ngIf="isPackageItem(item) && getPackageGames(item)?.length" class="mb-3">
                    <div class="text-gray-400 text-xs mb-1">Включает игры:</div>
                    <div class="flex flex-wrap gap-1">
                      <span
                        *ngFor="let game of getPackageGames(item)"
                        class="text-xs bg-slate-700 text-gray-300 px-2 py-1 rounded"
                      >
                        {{ game.title }}
                      </span>
                    </div>
                  </div>

                  <!-- Price -->
                  <div class="flex justify-between items-center">
                    <div class="text-right">
                      <p class="text-xl font-bold text-green-400">
                        {{ formatPrice(getItemPrice(item)) }}
                      </p>
                      <p *ngIf="isPackageItem(item) && getPackageDuration(item)" class="text-xs text-gray-400">
                        {{ getPackageDuration(item) }} дней доступа
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column - Order Summary / Registration -->
          <div class="lg:col-span-1">
            <div
              class="cart-summary bg-slate-800/50 border border-slate-600/50 rounded-lg p-6 sticky top-6"
            >
              <!-- Authenticated User - Order Summary -->
              <div *ngIf="isAuthenticated()">
                <h2 class="text-xl font-bold text-white mb-2">Общая стоимость</h2>

                <div class="space-y-2 mb-2">
                  <div class="text-gray-300">Итого:</div>
                  <ul class="space-y-2 ml-3">
                    <li
                      *ngFor="let item of getCartItems()"
                      class="flex items-center font-medium text-base text-gray-100"
                    >
                      <span class="w-1 h-1 bg-blue-400 rounded-full mr-2"></span>
                      <span class="flex-1">{{ getItemType(item) }} {{ getItemTitle(item) }}</span>
                    </li>
                  </ul>
                </div>

                <div class="border-t border-slate-600/50 pt-4 mb-6">
                  <div
                    class="flex justify-between items-center text-2xl font-bold text-white"
                  >
                    <span>{{ formatPrice(getTotalPrice()) }}₸</span>
                  </div>
                </div>

                <div class="space-y-3">
                  <button
                    (click)="goToGames()"
                    class="w-full px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
                  >
                    Продолжить покупки
                  </button>

                  <button
                    (click)="checkout()"
                    [disabled]="getCartItems().length === 0 || checkoutLoading"
                    class="w-full px-3 py-2 bg-emerald-500 hover:bg-emerald-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium flex items-center justify-center gap-2"
                  >
                    <span
                      *ngIf="checkoutLoading"
                      class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"
                    ></span>
                    {{ checkoutLoading ? "Оформление..." : "Перейти к оплате" }}
                  </button>
                </div>
              </div>

              <!-- Guest User - Registration Form -->
              <div *ngIf="!isAuthenticated()" class="relative">
                <h2 class="text-xl font-bold text-white mb-4">Оформление заказа</h2>

                <!-- Registration Notice -->
                <div class="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 mb-6">
                  <p class="text-blue-300 text-sm mb-3">
                    Для оформления заказа необходимо зарегистрироваться или войти в систему
                  </p>
                  <div class="flex gap-2">
                    <button
                      (click)="showLoginForm = false"
                      [class]="!showLoginForm ? 'bg-blue-600 text-white' : 'bg-transparent text-blue-300 border border-blue-500/50'"
                      class="px-3 py-1 rounded text-xs transition-colors"
                    >
                      Регистрация
                    </button>
                    <button
                      (click)="showLoginForm = true"
                      [class]="showLoginForm ? 'bg-blue-600 text-white' : 'bg-transparent text-blue-300 border border-blue-500/50'"
                      class="px-3 py-1 rounded text-xs transition-colors"
                    >
                      Уже есть аккаунт
                    </button>
                  </div>
                </div>

                <!-- Processing Overlay -->
                <div *ngIf="isProcessingRegistration" class="processing-overlay absolute inset-0 bg-slate-800/80 rounded-lg flex items-center justify-center z-50">
                  <div class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
                    <p class="processing-text text-white font-medium">Завершение регистрации...</p>
                    <p class="text-gray-300 text-sm mt-2">Автоматический вход и перенос корзины</p>
                  </div>
                </div>

                <!-- Login Form -->
                <div *ngIf="showLoginForm && !showCodeField" [class.opacity-50]="isProcessingRegistration">
                  <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="space-y-4">
                    <!-- Email -->
                    <div>
                      <label class="block text-gray-300 text-sm font-medium mb-2">Email</label>
                      <input
                        type="email"
                        formControlName="email"
                        class="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        [class.border-red-500]="hasFieldError('email')"
                        placeholder="<EMAIL>"
                      >
                      <div *ngIf="hasFieldError('email')" class="text-red-400 text-xs mt-1">
                        {{ getFieldError('email') }}
                      </div>
                    </div>

                    <!-- Password -->
                    <div>
                      <label class="block text-gray-300 text-sm font-medium mb-2">Пароль</label>
                      <input
                        type="password"
                        formControlName="password"
                        class="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        [class.border-red-500]="hasFieldError('password')"
                        placeholder="Ваш пароль"
                      >
                      <div *ngIf="hasFieldError('password')" class="text-red-400 text-xs mt-1">
                        {{ getFieldError('password') }}
                      </div>
                    </div>

                    <!-- Error Message -->
                    <div *ngIf="registrationError" class="text-red-400 text-sm">
                      {{ registrationError }}
                    </div>

                    <!-- Login Button -->
                    <button
                      type="submit"
                      [disabled]="loginForm.invalid || loginLoading || isProcessingRegistration"
                      class="w-full px-4 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium flex items-center justify-center gap-2"
                    >
                      <span *ngIf="loginLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                      {{ loginLoading ? 'Вход...' : 'Войти и перенести корзину' }}
                    </button>
                  </form>
                </div>

                <!-- Registration Form -->
                <div *ngIf="!showLoginForm && !showCodeField" [class.opacity-50]="isProcessingRegistration">
                  <form [formGroup]="registrationForm" (ngSubmit)="onRegister()" class="space-y-4">
                    <!-- Email -->
                    <div>
                      <label class="block text-gray-300 text-sm font-medium mb-2">Email</label>
                      <input
                        type="email"
                        formControlName="email"
                        class="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        [class.border-red-500]="hasFieldError('email')"
                        placeholder="<EMAIL>"
                      >
                      <div *ngIf="hasFieldError('email')" class="text-red-400 text-xs mt-1">
                        {{ getFieldError('email') }}
                      </div>
                    </div>

                    <!-- Password -->
                    <div>
                      <label class="block text-gray-300 text-sm font-medium mb-2">Пароль</label>
                      <input
                        type="password"
                        formControlName="password"
                        class="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        [class.border-red-500]="hasFieldError('password')"
                        placeholder="Минимум 8 символов"
                      >
                      <div *ngIf="hasFieldError('password')" class="text-red-400 text-xs mt-1">
                        {{ getFieldError('password') }}
                      </div>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                      <label class="block text-gray-300 text-sm font-medium mb-2">Подтвердите пароль</label>
                      <input
                        type="password"
                        formControlName="password_confirm"
                        class="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        [class.border-red-500]="registrationForm.hasError('passwordMismatch')"
                        placeholder="Повторите пароль"
                      >
                      <div *ngIf="registrationForm.hasError('passwordMismatch')" class="text-red-400 text-xs mt-1">
                        Пароли не совпадают
                      </div>
                    </div>

                    <!-- Error Message -->
                    <div *ngIf="registrationError" class="text-red-400 text-sm">
                      {{ registrationError }}
                    </div>

                    <!-- Register Button -->
                    <button
                      type="submit"
                      [disabled]="registrationForm.invalid || registrationLoading || isProcessingRegistration"
                      class="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium flex items-center justify-center gap-2"
                    >
                      <span *ngIf="registrationLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                      {{ registrationLoading ? 'Регистрация...' : 'Зарегистрироваться' }}
                    </button>
                  </form>
                </div>

                <!-- Verification Form -->
                <div *ngIf="showCodeField" [class.opacity-50]="isProcessingRegistration">
                  <div class="mb-4">
                    <p class="text-gray-300 text-sm">
                      Код подтверждения отправлен на <strong>{{ registeredEmail }}</strong>
                    </p>
                  </div>

                  <form [formGroup]="verificationForm" (ngSubmit)="onVerifyCode()" class="space-y-4">
                    <!-- Verification Code -->
                    <div>
                      <label class="block text-gray-300 text-sm font-medium mb-2">Код подтверждения</label>
                      <input
                        type="text"
                        formControlName="code"
                        maxlength="6"
                        class="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm text-center tracking-widest"
                        placeholder="000000"
                      >
                    </div>

                    <!-- Error Message -->
                    <div *ngIf="registrationError" class="text-red-400 text-sm">
                      {{ registrationError }}
                    </div>

                    <!-- Verify Button -->
                    <button
                      type="submit"
                      [disabled]="verificationForm.invalid || verificationLoading || isProcessingRegistration"
                      class="w-full px-4 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium flex items-center justify-center gap-2"
                    >
                      <span *ngIf="verificationLoading || isProcessingRegistration" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                      {{ isProcessingRegistration ? 'Завершение...' : (verificationLoading ? 'Проверка...' : 'Подтвердить и войти') }}
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
