import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { LibraryService } from '../../../../core/services/library.service';
import { GameService } from '../../../../core/services/game.service';
import { UserService } from '../../../../core/services/user.service';
import { UserSummaryService } from '../../../../core/services/user-summary.service';
import { ModalService } from '../../../../core/services/modal.service';
import { Game, GameFilters } from '../../../../core/models/game.model';
import { User, UserFilters } from '../../../../core/models/user.model';
import { AddToLibraryRequest } from '../../../../core/models/library.model';

@Component({
  selector: 'app-library-management',
  standalone: false,
  templateUrl: './library-management.component.html',
  styleUrl: './library-management.component.css'
})
export class LibraryManagementComponent implements OnInit, OnDestroy {
  // Add to library form
  showAddForm = false;
  addLoading = false;
  addError = '';

  // Games data
  games: Game[] = [];
  gamesLoading = false;
  gamesError = '';
  selectedGameId: number | null = null;

  // Users data
  users: User[] = [];
  usersLoading = false;
  usersError = '';
  selectedUserId: number | null = null;

  // Search functionality
  gameSearchTerm = '';
  userSearchTerm = '';
  private gameSearchSubject = new Subject<string>();
  private userSearchSubject = new Subject<string>();
  private gameSearchSubscription?: Subscription;
  private userSearchSubscription?: Subscription;

  // Pagination
  gamesCurrentPage = 1;
  usersCurrentPage = 1;
  pageSize = 10;
  totalGames = 0;
  totalUsers = 0;

  constructor(
    private libraryService: LibraryService,
    private gameService: GameService,
    private userService: UserService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.gameSearchSubscription?.unsubscribe();
    this.userSearchSubscription?.unsubscribe();
  }

  private setupSearch(): void {
    this.gameSearchSubscription = this.gameSearchSubject.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.gamesCurrentPage = 1;
      this.loadGames();
    });

    this.userSearchSubscription = this.userSearchSubject.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.usersCurrentPage = 1;
      this.loadUsers();
    });
  }

  toggleAddForm(): void {
    this.showAddForm = !this.showAddForm;
    if (this.showAddForm) {
      this.loadGames();
      this.loadUsers();
      this.resetForm();
    }
  }

  resetForm(): void {
    this.selectedGameId = null;
    this.selectedUserId = null;
    this.addError = '';
    this.gameSearchTerm = '';
    this.userSearchTerm = '';
  }

  loadGames(): void {
    this.gamesLoading = true;
    this.gamesError = '';

    const filters: GameFilters = {};
    if (this.gameSearchTerm.trim()) {
      filters.search = this.gameSearchTerm.trim();
    }
    filters.ordering = 'title';

    this.gameService.getGames(filters, this.gamesCurrentPage, this.pageSize).subscribe({
      next: (response) => {
        this.games = response.results;
        this.totalGames = response.count;
        this.gamesLoading = false;
      },
      error: (error) => {
        this.gamesError = error.message || 'Failed to load games';
        this.gamesLoading = false;
      }
    });
  }

  loadUsers(): void {
    this.usersLoading = true;
    this.usersError = '';

    const filters: UserFilters = {};
    if (this.userSearchTerm.trim()) {
      filters.search = this.userSearchTerm.trim();
    }
    filters.ordering = 'email';

    this.userService.getUsers(filters, this.usersCurrentPage, this.pageSize).subscribe({
      next: (response) => {
        this.users = response.results;
        this.totalUsers = response.count;
        this.usersLoading = false;
      },
      error: (error) => {
        this.usersError = error.message || 'Failed to load users';
        this.usersLoading = false;
      }
    });
  }

  onGameSearchChange(): void {
    this.gameSearchSubject.next(this.gameSearchTerm);
  }

  onUserSearchChange(): void {
    this.userSearchSubject.next(this.userSearchTerm);
  }

  selectGame(gameId: number): void {
    this.selectedGameId = gameId;
  }

  selectUser(userId: number): void {
    this.selectedUserId = userId;
  }

  getSelectedGame(): Game | null {
    return this.games.find(game => game.id === this.selectedGameId) || null;
  }

  getSelectedUser(): User | null {
    return this.users.find(user => user.id === this.selectedUserId) || null;
  }

  addGameToLibrary(): void {
    if (!this.selectedGameId || !this.selectedUserId) {
      this.addError = 'Выберите игру и пользователя';
      return;
    }

    this.addLoading = true;
    this.addError = '';

    const request: AddToLibraryRequest = {
      user_id: this.selectedUserId,
      game_id: this.selectedGameId
    };

    this.libraryService.addToLibrary(request).subscribe({
      next: (response) => {
        this.addLoading = false;
        const selectedGame = this.getSelectedGame();
        const selectedUser = this.getSelectedUser();

        // Refresh user summary to update library count
        this.userSummaryService.refreshSummary();

        this.modalService.success(
          'Успех',
          `Игра "${selectedGame?.title}" добавлена в библиотеку пользователя ${selectedUser?.email}`
        );

        this.resetForm();
        this.showAddForm = false;
      },
      error: (error) => {
        this.addLoading = false;
        this.addError = error.message || 'Не удалось добавить игру в библиотеку';
        this.modalService.error('Ошибка', this.addError);
      }
    });
  }

  // Pagination methods for games
  goToGamesPage(page: number): void {
    this.gamesCurrentPage = page;
    this.loadGames();
  }

  // Pagination methods for users
  goToUsersPage(page: number): void {
    this.usersCurrentPage = page;
    this.loadUsers();
  }

  getGamesTotalPages(): number {
    return Math.ceil(this.totalGames / this.pageSize);
  }

  getUsersTotalPages(): number {
    return Math.ceil(this.totalUsers / this.pageSize);
  }
}
