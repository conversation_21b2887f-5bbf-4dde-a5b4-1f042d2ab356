import { Component, OnInit } from '@angular/core';
import { GameService } from '../../../../core/services/game.service';
import { ModalService } from '../../../../core/services/modal.service';
import { Game, GameFilters, CreateGameRequest } from '../../../../core/models/game.model';
import { debounceTime, Subject, firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-games-management',
  standalone: false,
  templateUrl: './games-management.component.html',
  styleUrl: './games-management.component.css'
})
export class GamesManagementComponent implements OnInit {
  games: Game[] = [];
  gamesLoading = false;
  gamesError = '';

  // Pagination
  totalGames = 0;
  currentPage = 1;
  pageSize = 12;
  hasNext = false;
  hasPrevious = false;

  // Search and filtering
  searchTerm = '';
  sortBy = '-created_at';
  private searchSubject = new Subject<string>();

  // Add game form
  showAddForm = false;
  addGameLoading = false;
  addGameError = '';
  newGame: CreateGameRequest = {
    title: '',
    description: '',
    price: '',
    trial_available: false,
    requires_device: false,
    game_code: ''
  };
  selectedCoverImage: File | null = null;

  // Gallery images
  selectedGalleryFiles: File[] = [];
  galleryUploadProgress: { [key: string]: number } = {};
  galleryUploadLoading = false;

  // Game detail modal
  selectedGameId: number | null = null;
  showGameDetail = false;

  constructor(
    private gameService: GameService,
    private modalService: ModalService
  ) {
    // Setup search debouncing
    this.searchSubject.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.loadGames();
    });
  }

  ngOnInit(): void {
    this.loadGames();
  }

  loadGames(): void {
    this.gamesLoading = true;
    this.gamesError = '';

    const filters: GameFilters = {};

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.sortBy) {
      filters.ordering = this.sortBy;
    }

    this.gameService.getGames(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.games = response.results;
        this.totalGames = response.count;
        this.hasNext = response.next !== null;
        this.hasPrevious = response.previous !== null;
        this.gamesLoading = false;
      },
      error: (error) => {
        this.gamesError = error.message || 'Failed to load games';
        this.gamesLoading = false;
      }
    });
  }

  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onSortChange(): void {
    this.loadGames();
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.sortBy = '-created_at';
    this.currentPage = 1;
    this.loadGames();
  }

  // Pagination methods
  goToPage(page: number): void {
    if (page >= 1 && page <= this.getTotalPages()) {
      this.currentPage = page;
      this.loadGames();
    }
  }

  nextPage(): void {
    if (this.hasNext) {
      this.currentPage++;
      this.loadGames();
    }
  }

  previousPage(): void {
    if (this.hasPrevious) {
      this.currentPage--;
      this.loadGames();
    }
  }

  getTotalPages(): number {
    return Math.ceil(this.totalGames / this.pageSize);
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;

    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  // Add game form methods
  toggleAddForm(): void {
    this.showAddForm = !this.showAddForm;
    if (!this.showAddForm) {
      this.resetAddForm();
    }
  }

  resetAddForm(): void {
    this.newGame = {
      title: '',
      description: '',
      price: '',
      trial_available: false,
      requires_device: false,
      game_code: ''
    };
    this.selectedCoverImage = null;
    this.selectedGalleryFiles = [];
    this.galleryUploadProgress = {};
    this.addGameError = '';
  }

  // Game detail modal methods
  openGameDetail(gameId: number): void {
    this.selectedGameId = gameId;
    this.showGameDetail = true;
  }

  closeGameDetail(): void {
    this.selectedGameId = null;
    this.showGameDetail = false;
  }

  onGameUpdated(updatedGame: Game): void {
    // Update the game in the local array
    const index = this.games.findIndex(game => game.id === updatedGame.id);
    if (index !== -1) {
      this.games[index] = updatedGame;
    }
  }

  onGameDeleted(gameId: number): void {
    // Remove the game from the local array
    this.games = this.games.filter(game => game.id !== gameId);
    this.totalGames--;

    // If this was the last game on the current page and we're not on page 1, go to previous page
    if (this.games.length === 0 && this.currentPage > 1) {
      this.currentPage--;
      this.loadGames();
    }
  }

  onGameCodeInput(event: any): void {
    const input = event.target;
    const value = input.value.toUpperCase().slice(0, 6); // Convert to uppercase and limit to 6 characters
    this.newGame.game_code = value;
    input.value = value; // Update the input field display
  }

  onCoverImageSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.addGameError = 'Пожалуйста, выберите файл изображения';
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.addGameError = 'Размер файла не должен превышать 5MB';
        return;
      }

      this.selectedCoverImage = file;
      this.addGameError = '';
    }
  }

  onGalleryFilesSelected(event: any): void {
    const files = Array.from(event.target.files) as File[];
    if (files.length === 0) return;

    // Validate each file
    for (const file of files) {
      // Validate file type (images and videos)
      if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
        this.addGameError = 'Пожалуйста, выберите только файлы изображений или видео';
        return;
      }

      // Validate file size (max 10MB for videos, 5MB for images)
      const maxSize = file.type.startsWith('video/') ? 10 * 1024 * 1024 : 5 * 1024 * 1024;
      if (file.size > maxSize) {
        const maxSizeText = file.type.startsWith('video/') ? '10MB' : '5MB';
        this.addGameError = `Размер файла ${file.name} не должен превышать ${maxSizeText}`;
        return;
      }
    }

    // Add files to the selection
    this.selectedGalleryFiles = [...this.selectedGalleryFiles, ...files];
    this.addGameError = '';
  }

  removeGalleryFile(index: number): void {
    this.selectedGalleryFiles.splice(index, 1);
  }

  async uploadGalleryFiles(gameId: number): Promise<void> {
    if (this.selectedGalleryFiles.length === 0) {
      return;
    }

    this.galleryUploadLoading = true;

    try {
      for (let i = 0; i < this.selectedGalleryFiles.length; i++) {
        const file = this.selectedGalleryFiles[i];
        const fileKey = `${file.name}_${i}`;

        this.galleryUploadProgress[fileKey] = 0;

        try {
          await firstValueFrom(this.gameService.addGalleryItem(gameId, file));
          this.galleryUploadProgress[fileKey] = 100;
        } catch (error) {
          console.error(`Error uploading file ${file.name}:`, error);
          throw new Error(`Ошибка загрузки файла ${file.name}`);
        }
      }
    } finally {
      this.galleryUploadLoading = false;
    }
  }

  async submitAddGame(): Promise<void> {
    // Validate required fields
    if (!this.newGame.title.trim()) {
      this.addGameError = 'Название игры обязательно';
      return;
    }

    if (!this.newGame.description.trim()) {
      this.addGameError = 'Описание игры обязательно';
      return;
    }

    // Validate price format
    if (!this.newGame.price || this.newGame.price.trim() === '') {
      this.addGameError = 'Цена игры обязательна';
      return;
    }

    const priceNum = parseFloat(this.newGame.price);
    if (isNaN(priceNum) || priceNum < 0) {
      this.addGameError = 'Введите корректную цену (должна быть больше или равна 0)';
      return;
    }

    // Validate game code if provided
    if (this.newGame.game_code && this.newGame.game_code.trim()) {
      const gameCode = this.newGame.game_code.trim().toUpperCase();
      if (gameCode.length > 6) {
        this.addGameError = 'Код игры не может быть длиннее 6 символов';
        return;
      }
      // Update the game code to ensure it's uppercase
      this.newGame.game_code = gameCode;
    }

    this.addGameLoading = true;
    this.addGameError = '';

    // Prepare game data
    const gameData: CreateGameRequest = {
      ...this.newGame,
      price: priceNum.toFixed(2)
    };

    // Ensure game_code is uppercase if provided
    if (gameData.game_code) {
      gameData.game_code = gameData.game_code.toUpperCase();
    }

    // Add cover image if selected
    if (this.selectedCoverImage) {
      gameData.cover_image = this.selectedCoverImage;
    }

    this.gameService.createGame(gameData).subscribe({
      next: (createdGame) => {
        // Game created successfully
        this.completeGameCreation();
      },
      error: (error) => {
        this.addGameLoading = false;

        let errorMessage = '';

        // Handle validation errors
        if (error.error && typeof error.error === 'object') {
          const errors = error.error;
          if (errors.title) {
            errorMessage = 'Название: ' + errors.title.join(', ');
          } else if (errors.description) {
            errorMessage = 'Описание: ' + errors.description.join(', ');
          } else if (errors.price) {
            errorMessage = 'Цена: ' + errors.price.join(', ');
          } else if (errors.cover_image) {
            errorMessage = 'Изображение: ' + errors.cover_image.join(', ');
          } else if (errors.non_field_errors) {
            errorMessage = errors.non_field_errors.join(', ');
          } else {
            errorMessage = 'Ошибка при создании игры';
          }
        } else {
          errorMessage = error.message || 'Ошибка при создании игры';
        }

        // Set inline error for form display
        this.addGameError = errorMessage;

        // Also show beautiful modal error
        this.modalService.error('Ошибка создания игры', errorMessage);
      }
    });
  }



  private completeGameCreation(): void {
    this.addGameLoading = false;
    this.showAddForm = false;
    this.resetAddForm();

    // Refresh the games list
    this.currentPage = 1; // Go to first page to see the new game
    this.loadGames();

    this.modalService.success('Успех', 'Игра успешно создана!');
  }


}
