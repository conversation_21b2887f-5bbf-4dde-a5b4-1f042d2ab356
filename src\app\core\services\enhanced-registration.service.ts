import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { switchMap, tap, catchError } from 'rxjs/operators';
import { AuthService, RegisterRequest, VerifyCodeRequest } from './auth.service';
import { CartService } from './cart.service';
import { GuestCartService, GuestCartItem } from './guest-cart.service';
import { UserSummaryService } from './user-summary.service';

export interface EnhancedRegistrationResult {
  success: boolean;
  message: string;
  cartTransferred: boolean;
  transferredItemsCount: number;
}

@Injectable({
  providedIn: 'root'
})
export class EnhancedRegistrationService {
  
  constructor(
    private authService: AuthService,
    private cartService: CartService,
    private guestCartService: GuestCartService,
    private userSummaryService: UserSummaryService
  ) {}

  /**
   * Enhanced registration flow:
   * 1. Register user
   * 2. Verify email code
   * 3. Automatically log in
   * 4. Transfer guest cart items
   */
  registerAndLogin(
    registrationData: RegisterRequest,
    verificationCode: string
  ): Observable<EnhancedRegistrationResult> {
    
    // Store guest cart items before starting the process
    const guestCartItems = this.guestCartService.transferToUserCart();
    
    return this.authService.register(registrationData).pipe(
      switchMap(() => {
        // After successful registration, verify the code
        const verificationData: VerifyCodeRequest = {
          email: registrationData.email,
          code: verificationCode
        };
        
        return this.authService.verifyCode(verificationData);
      }),
      switchMap(() => {
        // After successful verification, automatically log in
        const loginData = {
          email: registrationData.email,
          password: registrationData.password
        };
        
        return this.authService.login(loginData);
      }),
      switchMap(() => {
        // After successful login, transfer guest cart items
        return this.transferGuestCartItems(guestCartItems);
      }),
      tap(() => {
        // Refresh user summary after everything is complete
        this.userSummaryService.refreshSummary();
      }),
      catchError(error => {
        // If anything fails, restore the guest cart
        this.restoreGuestCart(guestCartItems);

        // Provide more specific error messages
        let errorMessage = 'Произошла ошибка при регистрации';
        if (error.status === 400) {
          errorMessage = 'Неверные данные регистрации';
        } else if (error.status === 409) {
          errorMessage = 'Пользователь с таким email уже существует';
        } else if (error.message) {
          errorMessage = error.message;
        }

        return throwError(() => ({ ...error, message: errorMessage }));
      })
    );
  }

  /**
   * Transfer guest cart items to authenticated user cart
   */
  private transferGuestCartItems(guestItems: GuestCartItem[]): Observable<EnhancedRegistrationResult> {
    if (guestItems.length === 0) {
      return of({
        success: true,
        message: 'Регистрация завершена успешно',
        cartTransferred: false,
        transferredItemsCount: 0
      });
    }

    // Separate games and packages
    const gameIds: number[] = [];
    const packageIds: number[] = [];

    guestItems.forEach(item => {
      if (item.type === 'game' && item.game?.id) {
        gameIds.push(item.game.id);
      } else if (item.type === 'package' && item.gamePackage?.id) {
        packageIds.push(item.gamePackage.id);
      }
    });

    if (gameIds.length === 0 && packageIds.length === 0) {
      return of({
        success: true,
        message: 'Регистрация завершена успешно',
        cartTransferred: false,
        transferredItemsCount: 0
      });
    }

    // Use the add-multiple endpoint to transfer items
    return this.cartService.addMultipleToCart(gameIds, packageIds).pipe(
      switchMap(() => {
        return of({
          success: true,
          message: 'Регистрация завершена успешно. Товары из корзины перенесены.',
          cartTransferred: true,
          transferredItemsCount: guestItems.length
        });
      }),
      catchError(error => {
        console.error('Error transferring cart items:', error);
        // Even if cart transfer fails, registration was successful
        return of({
          success: true,
          message: 'Регистрация завершена успешно. Не удалось перенести товары из корзины.',
          cartTransferred: false,
          transferredItemsCount: 0
        });
      })
    );
  }

  /**
   * Restore guest cart items if the process fails
   */
  private restoreGuestCart(guestItems: GuestCartItem[]): void {
    try {
      // Restore items to guest cart
      guestItems.forEach(item => {
        if (item.type === 'game' && item.game) {
          this.guestCartService.addGame(item.game).subscribe();
        } else if (item.type === 'package' && item.gamePackage) {
          this.guestCartService.addPackage(item.gamePackage).subscribe();
        }
      });
    } catch (error) {
      console.error('Error restoring guest cart:', error);
    }
  }

  /**
   * Simple registration without automatic login (for cases where auto-login is not desired)
   */
  registerOnly(
    registrationData: RegisterRequest,
    verificationCode: string
  ): Observable<{ success: boolean; message: string }> {
    
    return this.authService.register(registrationData).pipe(
      switchMap(() => {
        const verificationData: VerifyCodeRequest = {
          email: registrationData.email,
          code: verificationCode
        };
        
        return this.authService.verifyCode(verificationData);
      }),
      switchMap(() => {
        return of({
          success: true,
          message: 'Регистрация завершена успешно'
        });
      })
    );
  }

  /**
   * Get current guest cart items count
   */
  getGuestCartItemsCount(): number {
    return this.guestCartService.getCartCount();
  }

  /**
   * Check if user has items in guest cart
   */
  hasGuestCartItems(): boolean {
    return this.getGuestCartItemsCount() > 0;
  }
}
