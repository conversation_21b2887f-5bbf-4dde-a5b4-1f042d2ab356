import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { GamePackageService } from '../../../../core/services/game-package.service';
import { GameService } from '../../../../core/services/game.service';
import { ModalService } from '../../../../core/services/modal.service';
import { 
  GamePackage, 
  CreateGamePackageRequest, 
  GamePackageFilters 
} from '../../../../core/models/game-package.model';
import { Game } from '../../../../core/models/game.model';

@Component({
  selector: 'app-game-packages-management',
  standalone: false,
  templateUrl: './game-packages-management.component.html',
  styleUrl: './game-packages-management.component.css'
})
export class GamePackagesManagementComponent implements OnInit, OnDestroy {
  packages: GamePackage[] = [];
  packagesLoading = false;
  packagesError = '';

  // Pagination
  totalPackages = 0;
  currentPage = 1;
  pageSize = 12;
  hasNext = false;
  hasPrevious = false;

  // Search and filtering
  searchTerm = '';
  sortBy = '-id';
  private searchSubject = new Subject<string>();

  // Add package form
  showAddForm = false;
  addPackageLoading = false;
  addPackageError = '';
  newPackage: CreateGamePackageRequest = {
    name: '',
    description: '',
    benefit_1: '',
    benefit_2: '',
    benefit_3: '',
    price: '',
    duration_days: 30,
    game_ids: [],
    max_selectable_games: 2
  };

  // Available games for selection
  availableGames: Game[] = [];
  gamesLoading = false;
  selectedGameIds: number[] = [];

  // Package detail modal
  selectedPackageId: number | null = null;
  showPackageDetail = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private packageService: GamePackageService,
    private gameService: GameService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.loadPackages();
    this.loadAvailableGames();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private setupSearch(): void {
    const searchSub = this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(() => {
      this.currentPage = 1;
      this.loadPackages();
    });
    this.subscriptions.push(searchSub);
  }

  loadPackages(): void {
    this.packagesLoading = true;
    this.packagesError = '';

    const filters: GamePackageFilters = {};

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.sortBy) {
      filters.ordering = this.sortBy;
    }

    this.packageService.getGamePackages(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.packages = response.results;
        this.totalPackages = response.count;
        this.hasNext = response.next !== null;
        this.hasPrevious = response.previous !== null;
        this.packagesLoading = false;
      },
      error: (error) => {
        this.packagesError = error.message || 'Failed to load packages';
        this.packagesLoading = false;
      }
    });
  }

  loadAvailableGames(): void {
    this.gamesLoading = true;
    this.gameService.getGames({}, 1, 100).subscribe({
      next: (response) => {
        this.availableGames = response.results;
        this.gamesLoading = false;
      },
      error: (error) => {
        console.error('Failed to load games:', error);
        this.gamesLoading = false;
      }
    });
  }

  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onSortChange(): void {
    this.loadPackages();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadPackages();
  }

  // Add package form methods
  showAddPackageForm(): void {
    this.showAddForm = true;
    this.resetAddForm();
  }

  hideAddPackageForm(): void {
    this.showAddForm = false;
    this.resetAddForm();
  }

  onGameSelectionChange(gameId: number, event: any): void {
    if (event.target.checked) {
      if (!this.selectedGameIds.includes(gameId)) {
        this.selectedGameIds.push(gameId);
      }
    } else {
      this.selectedGameIds = this.selectedGameIds.filter(id => id !== gameId);
    }
    this.newPackage.game_ids = [...this.selectedGameIds];
  }

  isGameSelected(gameId: number): boolean {
    return this.selectedGameIds.includes(gameId);
  }

  addPackage(): void {
    // Validate required fields
    if (!this.newPackage.name || !this.newPackage.name.trim()) {
      this.addPackageError = 'Package name is required';
      return;
    }

    if (!this.newPackage.description || !this.newPackage.description.trim()) {
      this.addPackageError = 'Package description is required';
      return;
    }

    if (!this.newPackage.price || this.newPackage.price.toString().trim() === '') {
      this.addPackageError = 'Package price is required';
      return;
    }

    // Validate price format
    const priceNum = parseFloat(this.newPackage.price.toString());
    if (isNaN(priceNum) || priceNum <= 0) {
      this.addPackageError = 'Please enter a valid price';
      return;
    }

    // Validate duration_days
    const durationNum = parseInt(this.newPackage.duration_days.toString());
    if (isNaN(durationNum) || durationNum <= 0) {
      this.addPackageError = 'Please enter a valid duration greater than 0';
      return;
    }

    // Validate max_selectable_games
    const maxGamesNum = parseInt(this.newPackage.max_selectable_games.toString());
    if (isNaN(maxGamesNum) || maxGamesNum <= 0) {
      this.addPackageError = 'Please enter a valid number of max selectable games greater than 0';
      return;
    }

    this.addPackageLoading = true;
    this.addPackageError = '';

    // Prepare package data
    const packageData: CreateGamePackageRequest = {
      ...this.newPackage,
      price: priceNum.toFixed(2),
      duration_days: durationNum,
      max_selectable_games: maxGamesNum
    };

    this.packageService.createGamePackage(packageData).subscribe({
      next: (newPackage) => {
        this.packages.unshift(newPackage);
        this.totalPackages++;
        this.hideAddPackageForm();
        this.addPackageLoading = false;
        this.modalService.success('Success', 'Package created successfully!');
      },
      error: (error) => {
        this.addPackageError = error.message || 'Failed to create package';
        this.addPackageLoading = false;
      }
    });
  }

  resetAddForm(): void {
    this.newPackage = {
      name: '',
      description: '',
      benefit_1: '',
      benefit_2: '',
      benefit_3: '',
      price: '',
      duration_days: 30,
      game_ids: [],
      max_selectable_games: 2
    };
    this.selectedGameIds = [];
    this.addPackageError = '';
  }

  // Package detail modal methods
  openPackageDetail(packageId: number): void {
    this.selectedPackageId = packageId;
    this.showPackageDetail = true;
  }

  closePackageDetail(): void {
    this.selectedPackageId = null;
    this.showPackageDetail = false;
  }

  onPackageUpdated(updatedPackage: GamePackage): void {
    // Update the package in the local array
    const index = this.packages.findIndex(pkg => pkg.id === updatedPackage.id);
    if (index !== -1) {
      this.packages[index] = updatedPackage;
    }
  }

  onPackageDeleted(packageId: number): void {
    // Remove the package from the local array
    this.packages = this.packages.filter(pkg => pkg.id !== packageId);
    this.totalPackages--;
    this.closePackageDetail();
  }

  deletePackage(packageId: number): void {
    this.modalService.confirm(
      'Delete Package',
      'Are you sure you want to delete this package? This action cannot be undone.',
      'Delete',
      'Cancel'
    ).then(confirmed => {
      if (confirmed) {
        this.packageService.deleteGamePackage(packageId).subscribe({
          next: () => {
            this.onPackageDeleted(packageId);
            this.modalService.success('Success', 'Package deleted successfully!');
          },
          error: (error) => {
            this.modalService.error('Error', 'Failed to delete package: ' + error.message);
          }
        });
      }
    });
  }

  getTotalPages(): number {
    return Math.ceil(this.totalPackages / this.pageSize);
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }
}
