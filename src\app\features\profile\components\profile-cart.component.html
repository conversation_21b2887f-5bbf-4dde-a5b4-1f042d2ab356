<!-- Profile Cart -->
<div class="max-w-6xl space-y-3 lg:space-y-4">
  <!-- Header -->
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
    <div>
      <h1 class="text-xl lg:text-2xl font-bold text-white mb-1">
        Корзина
      </h1>
      <p class="text-sm lg:text-base text-gray-300">
        {{ cart.total_items }} {{ cart.total_items === 1 ? 'товар' : cart.total_items < 5 ? 'товара' : 'товаров' }}
      </p>
    </div>

    <button
      (click)="goToGames()"
      class="px-3 lg:px-4 py-1.5 lg:py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors text-sm lg:text-base"
    >
      Продолжить покупки
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="!loading && error" class="text-center py-12">
    <div class="text-red-400 mb-4">{{ error }}</div>
    <button
      (click)="loadCart()"
      class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- Empty Cart -->
  <div *ngIf="!loading && !error && cart.items.length === 0" class="text-center py-8">
    <div class="text-gray-400 mb-4">
      <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
      </svg>
      <p class="text-lg">Ваша корзина пуста</p>
    </div>
    <button
      (click)="goToGames()"
      class="px-5 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors text-sm lg:text-base font-medium"
    >
      Перейти к играм
    </button>
  </div>

  <!-- Cart Items -->
  <div *ngIf="!loading && !error && cart.items.length > 0" class="space-y-3 lg:space-y-4">
    <!-- Cart Items List -->
    <div class="space-y-2 lg:space-y-3">
      <div
        *ngFor="let item of cart.items"
        class="bg-slate-800/40 border border-slate-600/50 rounded-md p-3 lg:p-4 hover:border-slate-500/60 transition-all"
      >
        <div class="flex flex-col sm:flex-row gap-3 lg:gap-4">
          <!-- Item Image -->
          <div class="w-full sm:w-20 lg:w-24 h-20 sm:h-20 lg:h-24 bg-slate-700 rounded-md overflow-hidden flex-shrink-0">
            <img
              *ngIf="getItemImage(item)"
              [src]="getItemImage(item)!"
              [alt]="getItemTitle(item)"
              class="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform"
              (click)="isGameItem(item) && viewGameDetails(item.game!)"
            >
            <div
              *ngIf="!getItemImage(item)"
              class="w-full h-full flex items-center justify-center text-gray-400 text-xs text-center"
            >
              <span *ngIf="isGameItem(item)">Нет изображения</span>
              <span *ngIf="isPackageItem(item)">Пакет игр</span>
            </div>
          </div>

          <!-- Item Info -->
          <div class="flex-1">
            <div class="flex flex-col md:flex-row md:items-start md:justify-between gap-3">
              <div class="flex-1">
                <h3
                  class="text-base font-medium text-white mb-1 cursor-pointer hover:text-blue-400 transition-colors"
                  (click)="isGameItem(item) && viewGameDetails(item.game!)"
                >
                  {{ getItemTitle(item) }}
                </h3>
                <p *ngIf="getItemDescription(item)" class="text-gray-300 text-xs mb-2 line-clamp-2">
                  {{ getItemDescription(item) }}
                </p>
                <div class="text-blue-400 font-medium text-sm">
                  {{ getItemPrice(item) }}₽
                  <span *ngIf="isPackageItem(item)" class="text-gray-400 text-xs ml-1">
                    ({{ item.game_package_obj?.duration_days }} дней)
                  </span>
                </div>
                <!-- Package games list -->
                <div *ngIf="isPackageItem(item) && item.game_package_obj?.games?.length" class="mt-2">
                  <div class="text-gray-400 text-xs mb-1">Включает игры:</div>
                  <div class="flex flex-wrap gap-1">
                    <span
                      *ngFor="let game of item.game_package_obj!.games"
                      class="text-xs bg-slate-700 text-gray-300 px-2 py-1 rounded"
                    >
                      {{ game.title }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- Price and Actions -->
              <div class="flex flex-col md:items-end gap-2">
                <!-- Price -->
                <div class="text-right">
                  <div class="text-blue-400 font-bold text-lg">{{ getItemPrice(item) }}₽</div>
                </div>

                <!-- Remove Button -->
                <button
                  (click)="removeItem(item)"
                  class="px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md transition-colors"
                >
                  Удалить
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cart Summary -->
    <div class="bg-slate-800/40 border border-slate-600/50 rounded-md p-3 lg:p-4">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3">
        <div>
          <div class="text-sm lg:text-base text-gray-300 mb-1">
            Всего товаров: {{ cart.total_items }}
          </div>
          <div class="text-lg lg:text-xl font-bold text-white">
            Итого: <span class="text-blue-400">{{ cart.total_price }}₽</span>
          </div>
        </div>

        <div class="flex flex-col sm:flex-row gap-2 lg:gap-3">
          <button
            (click)="clearCart()"
            [disabled]="cart.items.length === 0"
            class="px-3 lg:px-4 py-1.5 lg:py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-800 disabled:cursor-not-allowed text-white rounded-md transition-colors text-sm lg:text-base"
          >
            Очистить корзину
          </button>

          <button
            (click)="checkout()"
            [disabled]="cart.items.length === 0 || checkoutLoading"
            class="px-4 lg:px-5 py-1.5 lg:py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-md transition-colors font-medium flex items-center justify-center gap-2 text-sm lg:text-base"
          >
            <span *ngIf="checkoutLoading" class="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></span>
            {{ checkoutLoading ? 'Оформление...' : 'Оформить заказ' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
