<!-- Profile Cart -->
<div class="max-w-6xl">
  <!-- Header -->
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-white mb-2">
      Корзина
    </h1>
    <p class="text-gray-300">
      {{ cart.total_items }} {{ cart.total_items === 1 ? 'товар' : cart.total_items < 5 ? 'товара' : 'товаров' }}
    </p>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="!loading && error" class="text-center py-12">
    <div class="text-red-400 mb-4">{{ error }}</div>
    <button
      (click)="loadCart()"
      class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- Empty Cart -->
  <div *ngIf="!loading && !error && cart.items.length === 0" class="text-center py-8">
    <div class="text-gray-400 mb-4">
      <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
      </svg>
      <p class="text-lg">Ваша корзина пуста</p>
    </div>
    <button
      (click)="goToGames()"
      class="px-5 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors font-medium"
    >
      Перейти к играм
    </button>
  </div>

  <!-- Cart Content - Two Column Layout -->
  <div *ngIf="!loading && !error && cart.items.length > 0" class="cart-grid grid grid-cols-1 lg:grid-cols-3 gap-6">

    <!-- Left Column - Cart Items -->
    <div class="lg:col-span-2 space-y-4">
      <div
        *ngFor="let item of cart.items"
        class="cart-item bg-slate-800/50 border border-slate-600/50 rounded-lg p-4 hover:border-slate-500/60 transition-all"
      >
        <div class="cart-item-content flex gap-4">
          <!-- Item Image -->
          <div class="cart-item-image w-32 h-24 bg-slate-700 rounded-lg overflow-hidden flex-shrink-0">
            <img
              *ngIf="getItemImage(item)"
              [src]="getItemImage(item)!"
              [alt]="getItemTitle(item)"
              class="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform"
              (click)="isGameItem(item) && viewGameDetails(item.game!)"
            >
            <div
              *ngIf="!getItemImage(item)"
              class="w-full h-full flex items-center justify-center text-gray-400 text-xs text-center"
            >
              <span *ngIf="isGameItem(item)">Нет изображения</span>
              <span *ngIf="isPackageItem(item)">Пакет игр</span>
            </div>
          </div>

          <!-- Item Info -->
          <div class="flex-1">
            <div class="flex justify-between items-start">
              <div class="flex-1 pr-4">
                <h3
                  class="text-lg font-semibold text-white mb-2 cursor-pointer hover:text-blue-400 transition-colors"
                  (click)="isGameItem(item) && viewGameDetails(item.game!)"
                >
                  {{ getItemTitle(item) }}
                </h3>
                <p *ngIf="getItemDescription(item)" class="text-gray-300 text-sm mb-3 line-clamp-2">
                  {{ getItemDescription(item) }}
                </p>

                <!-- Package games list -->
                <div *ngIf="isPackageItem(item) && item.game_package_obj?.games?.length" class="mb-3">
                  <div class="text-gray-400 text-sm mb-2">Итого:</div>
                  <ul class="space-y-1">
                    <li
                      *ngFor="let game of item.game_package_obj!.games"
                      class="text-sm text-gray-300 flex items-center"
                    >
                      <span class="w-1 h-1 bg-blue-400 rounded-full mr-2"></span>
                      {{ game.title }}
                    </li>
                  </ul>
                </div>

                <div class="price-highlight text-green-400 font-bold text-xl">
                  {{ getItemPrice(item) }}₽
                  <span *ngIf="isPackageItem(item)" class="text-gray-400 text-sm ml-2 font-normal">
                    ({{ item.game_package_obj?.duration_days }} дней)
                  </span>
                </div>
              </div>

              <!-- Remove Button -->
              <button
                (click)="removeItem(item)"
                class="p-2 text-gray-400 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all"
                title="Удалить из корзины"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column - Order Summary -->
    <div class="lg:col-span-1">
      <div class="bg-slate-800/50 border border-slate-600/50 rounded-lg p-6 sticky top-6">
        <h2 class="text-xl font-bold text-white mb-6">Общая стоимость</h2>

        <div class="space-y-4 mb-6">
          <div class="text-gray-300">
            Итого:
          </div>
          <ul class="space-y-2">
            <li
              *ngFor="let item of cart.items"
              class="flex items-center text-sm text-gray-300"
            >
              <span class="w-1 h-1 bg-blue-400 rounded-full mr-2"></span>
              <span class="flex-1">{{ getItemTitle(item) }}</span>
            </li>
          </ul>
        </div>

        <div class="border-t border-slate-600/50 pt-4 mb-6">
          <div class="flex justify-between items-center text-2xl font-bold text-white">
            <span>{{ cart.total_price }}₽</span>
          </div>
        </div>

        <div class="space-y-3">
          <button
            (click)="goToGames()"
            class="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
          >
            Продолжить покупки
          </button>

          <button
            (click)="checkout()"
            [disabled]="cart.items.length === 0 || checkoutLoading"
            class="w-full px-4 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium flex items-center justify-center gap-2"
          >
            <span *ngIf="checkoutLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
            {{ checkoutLoading ? 'Оформление...' : 'Перейти к оплате' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
