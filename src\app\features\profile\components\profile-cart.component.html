<!-- Profile Cart -->
<div class="max-w-6xl">
  <!-- Header -->
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-white mb-2">Корзина</h1>
    <p class="text-gray-300">
      {{ cart.total_items }}
      {{
        cart.total_items === 1
          ? "товар"
          : cart.total_items < 5
          ? "товара"
          : "товаров"
      }}
    </p>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center py-12">
    <div
      class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"
    ></div>
  </div>

  <!-- Error State -->
  <div *ngIf="!loading && error" class="text-center py-12">
    <div class="text-red-400 mb-4">{{ error }}</div>
    <button
      (click)="loadCart()"
      class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- Empty Cart -->
  <div
    *ngIf="!loading && !error && cart.items.length === 0"
    class="text-center py-8"
  >
    <div class="text-gray-400 mb-4">
      <img
        src="assets/icons/cart.svg"
        width="50"
        alt="Empty Cart"
        class="mx-auto mb-3 opacity-40"
      />
      <p class="text-lg">Ваша корзина пуста</p>
    </div>
    <button
      (click)="goToGames()"
      class="px-4 text-sm py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors font-medium"
    >
      Перейти к играм
    </button>
  </div>

  <!-- Cart Content - Two Column Layout -->
  <div
    *ngIf="!loading && !error && cart.items.length > 0"
    class="cart-grid grid grid-cols-1 lg:grid-cols-3 gap-6"
  >
    <!-- Left Column - Cart Items -->
    <div class="lg:col-span-2 space-y-4">
      <div
        *ngFor="let item of cart.items"
        class="cart-item bg-slate-800/50 border border-slate-600/50 rounded-lg p-4 hover:border-slate-500/60 transition-all"
      >
        <div class="relative">
          <!-- Remove Button - Top Right -->
          <button
            (click)="removeItem(item)"
            class="remove-button absolute top-3 right-3 p-2 text-gray-400 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all z-10"
            title="Удалить из корзины"
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              ></path>
            </svg>
          </button>

          <!-- Item Image - Top Left -->
          <div
            class="cart-item-image w-48 h-36 bg-slate-700 rounded-lg overflow-hidden mb-4"
          >
            <img
              *ngIf="getItemImage(item)"
              [src]="getItemImage(item)!"
              [alt]="getItemTitle(item)"
              class="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform"
              (click)="isGameItem(item) && viewGameDetails(item.game!)"
            />
            <div
              *ngIf="!getItemImage(item)"
              class="w-full h-full flex items-center justify-center text-gray-400 text-xs text-center"
            >
              <span *ngIf="isGameItem(item)">Нет изображения</span>
              <span *ngIf="isPackageItem(item)">Пакет игр</span>
            </div>
          </div>

          <!-- Item Content -->
          <div class="space-y-3">
            <!-- Game Title -->
            <h3
              class="text-xl font-semibold text-white cursor-pointer hover:text-blue-400 transition-colors"
              (click)="isGameItem(item) && viewGameDetails(item.game!)"
            >
              {{ getItemTitle(item) }} 
            </h3>

            <!-- Game Description -->
            <p
              *ngIf="getItemDescription(item)"
              class="text-gray-300 text-sm line-clamp-2"
            >
              {{ getItemDescription(item) }}
            </p>

            <!-- System Requirements (for games) -->
            <div
              *ngIf="isGameItem(item) && item.game_obj?.system_requirements"
              class="text-gray-400 text-sm"
            >
              {{ item.game_obj?.system_requirements }}
            </div>  
          </div>

          <!-- Price - Bottom Right -->
          <div class="flex justify-end mt-4">
            <div class="price-highlight text-emerald-400 font-bold text-xl">
              {{ getItemPrice(item) }}₸
              <span
                *ngIf="isPackageItem(item)"
                class="text-gray-400 text-sm ml-2 font-normal block"
              >
                ({{ item.game_package_obj?.duration_days }} дней)
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column - Order Summary -->
    <div class="lg:col-span-1">
      <div
        class="cart-summary bg-slate-800/50 border border-slate-600/50 rounded-lg p-6 sticky top-6"
      >
        <h2 class="text-xl font-bold text-white mb-2">Общая стоимость</h2>

        <div class="space-y-2 mb-2">
          <div class="text-gray-300">Итого:</div>
          <ul class="space-y-2">
            <li
              *ngFor="let item of cart.items"
              class="flex items-center font-medium text-base text-gray-100"
            >
              <span class="w-1 h-1 bg-blue-400 rounded-full mr-2"></span>
              <span class="flex-1">Игра {{ getItemTitle(item) }}</span>
            </li>
          </ul>
        </div>

        <div class="border-t border-slate-600/50 pt-4 mb-6">
          <div
            class="flex justify-between items-center text-2xl font-bold text-white"
          >
            <span>{{ cart.total_price }}₸</span>
          </div>
        </div>

        <div class="space-y-3">
          <button
            (click)="goToGames()"
            class="w-full px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
          >
            Продолжить покупки
          </button>

          <button
            (click)="checkout()"
            [disabled]="cart.items.length === 0 || checkoutLoading"
            class="w-full px-3 py-2 bg-emerald-500 hover:bg-emerald-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium flex items-center justify-center gap-2"
          >
            <span
              *ngIf="checkoutLoading"
              class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"
            ></span>
            {{ checkoutLoading ? "Оформление..." : "Перейти к оплате" }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
