/* Profile Cart Styles */

/* Line clamp utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hover effects for images */
img.hover\:scale-105:hover {
  transform: scale(1.05);
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  /* Mobile-first responsive design */
  .space-y-6 > * + * {
    margin-top: 1rem;
  }

  .space-y-4 > * + * {
    margin-top: 0.75rem;
  }

  /* Compact layout on mobile */
  .bg-slate-800\/40 {
    padding: 1rem;
  }

  /* Stack elements vertically on mobile */
  .flex-col-md\:flex-row {
    flex-direction: column;
  }

  .md\:items-end {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: flex-start;
  }

  .md\:justify-between {
    justify-content: flex-start;
  }

  /* Smaller text on mobile */
  .text-3xl {
    font-size: 1.875rem;
  }

  .text-2xl {
    font-size: 1.5rem;
  }

  .text-xl {
    font-size: 1.25rem;
  }

  /* Touch-friendly buttons */
  button {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  /* Tablet adjustments */
  .space-y-6 > * + * {
    margin-top: 1.25rem;
  }

  .bg-slate-800\/40 {
    padding: 1.25rem;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .hover\:border-slate-500\/60:hover {
    border-color: inherit;
  }

  .hover\:bg-blue-700:hover {
    background-color: inherit;
  }

  .hover\:bg-gray-700:hover {
    background-color: inherit;
  }

  .hover\:bg-green-700:hover {
    background-color: inherit;
  }

  .hover\:scale-105:hover {
    transform: none;
  }
}

/* Cart-specific enhancements */
.cart-item {
  transition: all 0.3s ease;
}

.cart-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Sticky summary positioning */
.sticky {
  position: sticky;
}

/* Enhanced remove button */
.remove-button {
  transition: all 0.2s ease;
}

.remove-button:hover {
  transform: scale(1.1);
}

/* Price highlight */
.price-highlight {
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Mobile cart layout adjustments */
@media (max-width: 1023px) {
  .cart-grid {
    grid-template-columns: 1fr;
  }

  .cart-summary {
    position: static;
    margin-top: 1.5rem;
  }

  .cart-item-image {
    width: 120px !important;
    height: 90px !important;
  }

  .cart-item-content {
    flex-direction: column;
    gap: 1rem;
  }
}

/* Tablet adjustments */
@media (max-width: 768px) {
  .cart-item-image {
    width: 100px !important;
    height: 75px !important;
  }
}

/* Small mobile adjustments */
@media (max-width: 480px) {
  .cart-item-image {
    width: 80px !important;
    height: 60px !important;
  }

  .cart-item {
    padding: 1rem !important;
  }
}
