import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class CartRoutingService {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  /**
   * Navigate to the appropriate cart based on authentication status
   */
  navigateToCart(): void {
    if (this.authService.isAuthenticated()) {
      // Authenticated users go to profile cart
      this.router.navigate(['/profile/cart']);
    } else {
      // Non-authenticated users go to guest cart
      this.router.navigate(['/cart']);
    }
  }

  /**
   * Get the appropriate cart route based on authentication status
   */
  getCartRoute(): string {
    if (this.authService.isAuthenticated()) {
      return '/profile/cart';
    } else {
      return '/cart';
    }
  }

  /**
   * Check if user is currently on a cart page (either guest or authenticated)
   */
  isOnCartPage(): boolean {
    const currentUrl = this.router.url;
    return currentUrl === '/cart' || currentUrl === '/profile/cart';
  }
}
