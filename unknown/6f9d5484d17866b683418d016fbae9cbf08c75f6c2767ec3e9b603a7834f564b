import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { GameAccessService } from '../../../../core/services/game-access.service';
import { UserService } from '../../../../core/services/user.service';
import { GameService } from '../../../../core/services/game.service';
import { ModalService } from '../../../../core/services/modal.service';
import { GameAccess, GameAccessFilters, CreateGameAccessRequest, UpdateGameAccessRequest } from '../../../../core/models/game-access.model';
import { User } from '../../../../core/models/user.model';
import { Game } from '../../../../core/models/game.model';

@Component({
  selector: 'app-game-access-management',
  standalone: false,
  templateUrl: './game-access-management.component.html',
  styleUrl: './game-access-management.component.css'
})
export class GameAccessManagementComponent implements OnInit, OnDestroy {
  gameAccess: GameAccess[] = [];
  gameAccessLoading = false;
  gameAccessError = '';
  totalGameAccess = 0;
  currentPage = 1;
  pageSize = 10;

  // Available users and games for dropdowns
  users: User[] = [];
  games: Game[] = [];
  usersLoading = false;
  gamesLoading = false;

  // Make Math available in template
  Math = Math;

  // Search and filtering
  searchTerm = '';
  selectedAccessType: 'all' | 'oneday' | 'subscription' = 'all';
  selectedAccessStatus: 'all' | 'active' | 'expired' = 'all';
  sortBy: 'access_start' | '-access_start' | 'access_end' | '-access_end' | 'user_email' | '-user_email' | 'game_title' | '-game_title' = '-access_start';

  // Search debouncing
  private searchSubject = new Subject<string>();
  private searchSubscription?: Subscription;

  // Add/Edit form
  showAddForm = false;
  editingAccess: GameAccess | null = null;
  formData: CreateGameAccessRequest = {
    user: 0,
    game: 0,
    access_type: 'oneday'
  };
  formLoading = false;
  formError = '';

  constructor(
    private gameAccessService: GameAccessService,
    private userService: UserService,
    private gameService: GameService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.loadGameAccess();
    this.loadUsers();
    this.loadGames();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.searchSubscription?.unsubscribe();
  }

  private setupSearch(): void {
    this.searchSubscription = this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(() => {
      this.currentPage = 1;
      this.loadGameAccess();
    });
  }

  loadGameAccess(): void {
    this.gameAccessLoading = true;
    this.gameAccessError = '';

    const filters: GameAccessFilters = {
      search: this.searchTerm || undefined,
      ordering: this.sortBy
    };

    // Add access type filter
    if (this.selectedAccessType !== 'all') {
      filters.access_type = this.selectedAccessType;
    }

    // Add access status filter
    if (this.selectedAccessStatus !== 'all') {
      filters.has_access = this.selectedAccessStatus === 'active';
    }

    this.gameAccessService.getGameAccess(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.gameAccess = response.results;
        this.totalGameAccess = response.count;
        this.gameAccessLoading = false;
      },
      error: (error: any) => {
        console.error('Failed to load game access:', error);
        this.gameAccessError = error.message || 'Failed to load game access';
        this.gameAccessLoading = false;
      }
    });
  }

  loadUsers(): void {
    this.usersLoading = true;
    this.userService.getUsers(undefined, 1, 100).subscribe({
      next: (response) => {
        this.users = response.results;
        this.usersLoading = false;
      },
      error: (error: any) => {
        console.error('Failed to load users:', error);
        this.usersLoading = false;
      }
    });
  }

  loadGames(): void {
    this.gamesLoading = true;
    this.gameService.getGames(undefined, 1, 100).subscribe({
      next: (response) => {
        this.games = response.results;
        this.gamesLoading = false;
      },
      error: (error: any) => {
        console.error('Failed to load games:', error);
        this.gamesLoading = false;
      }
    });
  }

  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onAccessTypeFilterChange(): void {
    this.currentPage = 1;
    this.loadGameAccess();
  }

  onAccessStatusFilterChange(): void {
    this.currentPage = 1;
    this.loadGameAccess();
  }

  onSortChange(): void {
    this.currentPage = 1;
    this.loadGameAccess();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadGameAccess();
  }

  get totalPages(): number {
    return Math.ceil(this.totalGameAccess / this.pageSize);
  }

  get pages(): number[] {
    const totalPages = this.totalPages;
    const currentPage = this.currentPage;
    const pages: number[] = [];
    
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }

  getAccessTypeText(accessType: string): string {
    switch (accessType) {
      case 'oneday': return 'Один день';
      case 'subscription': return 'Подписка';
      default: return accessType;
    }
  }

  getAccessTypeClass(accessType: string): string {
    switch (accessType) {
      case 'oneday': return 'text-yellow-400';
      case 'subscription': return 'text-green-400';
      default: return 'text-gray-400';
    }
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  showAddAccessForm(): void {
    this.showAddForm = true;
    this.editingAccess = null;
    this.resetForm();
  }

  editAccess(access: GameAccess): void {
    this.editingAccess = access;
    this.showAddForm = true;
    this.formData = {
      user: access.user,
      game: access.game,
      access_type: access.access_type,
      access_start: access.access_start,
      access_end: access.access_end
    };
  }

  cancelForm(): void {
    this.showAddForm = false;
    this.editingAccess = null;
    this.resetForm();
  }

  resetForm(): void {
    this.formData = {
      user: 0,
      game: 0,
      access_type: 'oneday'
    };
    this.formError = '';
  }

  submitForm(): void {
    if (!this.formData.user || !this.formData.game) {
      this.formError = 'Пожалуйста, выберите пользователя и игру';
      return;
    }

    this.formLoading = true;
    this.formError = '';

    if (this.editingAccess) {
      // Update existing access
      const updateData: UpdateGameAccessRequest = {
        access_type: this.formData.access_type,
        access_start: this.formData.access_start,
        access_end: this.formData.access_end
      };

      this.gameAccessService.updateGameAccess(this.editingAccess.id, updateData).subscribe({
        next: () => {
          this.modalService.success('Успех', 'Доступ к игре успешно обновлен');
          this.cancelForm();
          this.loadGameAccess();
          this.formLoading = false;
        },
        error: (error: any) => {
          this.formError = error.message || 'Ошибка при обновлении доступа';
          this.formLoading = false;
        }
      });
    } else {
      // Create new access
      this.gameAccessService.createGameAccess(this.formData).subscribe({
        next: () => {
          this.modalService.success('Успех', 'Доступ к игре успешно создан');
          this.cancelForm();
          this.loadGameAccess();
          this.formLoading = false;
        },
        error: (error: any) => {
          this.formError = error.message || 'Ошибка при создании доступа';
          this.formLoading = false;
        }
      });
    }
  }

  deleteAccess(access: GameAccess): void {
    this.modalService.confirm(
      'Удалить доступ к игре?',
      `Вы уверены, что хотите удалить доступ пользователя ${access.user_email} к игре "${access.game_title}"?`,
      'Удалить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.gameAccessService.deleteGameAccess(access.id).subscribe({
          next: () => {
            this.modalService.success('Успех', 'Доступ к игре успешно удален');
            this.loadGameAccess();
          },
          error: (error: any) => {
            this.modalService.error('Ошибка при удалении доступа', error.message);
          }
        });
      }
    });
  }
}
