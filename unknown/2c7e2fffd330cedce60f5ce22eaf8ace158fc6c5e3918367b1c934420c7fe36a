/* Component-specific styles for game files management */

/* File upload styling */
input[type="file"]::-webkit-file-upload-button {
  background: #2563eb;
  border: none;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

input[type="file"]::-webkit-file-upload-button:hover {
  background: #1d4ed8;
}

/* Table responsive styling */
@media (max-width: 1024px) {
  .table-container {
    overflow-x: auto;
  }
}

/* Loading spinner animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Custom scrollbar for table */
.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.5);
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* Status indicators */
.status-active {
  color: #10b981;
}

.status-inactive {
  color: #ef4444;
}

/* Button hover effects */
.btn-download:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.btn-delete:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

/* Form validation styles */
.form-error {
  border-color: #ef4444;
  box-shadow: 0 0 0 1px #ef4444;
}

.form-success {
  border-color: #10b981;
  box-shadow: 0 0 0 1px #10b981;
}

/* Platform badges */
.platform-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.platform-windows {
  background-color: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
}

.platform-mac {
  background-color: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
}

.platform-linux {
  background-color: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.platform-android {
  background-color: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.platform-ios {
  background-color: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.platform-web {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

/* File size formatting */
.file-size {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
  .grid-responsive {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Card hover effects */
.game-file-card {
  transition: all 0.2s ease-in-out;
}

.game-file-card:hover {
  background-color: rgba(51, 65, 85, 0.3);
  transform: translateY(-1px);
}

/* Progress bar for file uploads */
.upload-progress {
  width: 100%;
  height: 4px;
  background-color: rgba(51, 65, 85, 0.5);
  border-radius: 2px;
  overflow: hidden;
}

.upload-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.3s ease;
}

/* Tooltip styles */
.tooltip {
  position: relative;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 10;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Animation for form toggle */
.form-slide-enter {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
