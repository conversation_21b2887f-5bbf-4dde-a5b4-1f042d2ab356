/* User Detail Component Styles */

/* Form styling improvements */
.form-section {
  @apply bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6;
}

/* Radio button styling */
input[type="radio"] {
  @apply w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 focus:ring-blue-500 focus:ring-2;
}

input[type="radio"]:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Input field focus states */
input:focus {
  @apply outline-none;
}

/* Loading spinner for save button */
.save-spinner {
  @apply animate-spin h-4 w-4;
}

/* Badge styling */
.user-badge {
  @apply inline-flex px-3 py-1 text-sm font-semibold rounded;
}

.user-code-badge {
  @apply bg-blue-900/50 text-blue-400 border border-blue-500/50;
}

.status-badge-active {
  @apply bg-green-900/50 text-green-400 border border-green-500/50;
}

.status-badge-inactive {
  @apply bg-red-900/50 text-red-400 border border-red-500/50;
}

/* Role text colors */
.role-superuser {
  @apply text-red-400;
}

.role-staff {
  @apply text-yellow-400;
}

.role-user {
  @apply text-blue-400;
}

/* Form validation */
.form-error {
  @apply mt-1 text-red-400 text-xs;
}

/* Readonly input styling */
.readonly-input {
  @apply bg-slate-700/30 border-slate-600/30 cursor-not-allowed;
}

.editable-input {
  @apply bg-slate-800/60 border-slate-600/50;
}

.editable-input:focus {
  @apply ring-2 ring-blue-500 border-transparent;
}

/* Button states */
.btn-primary {
  @apply px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors;
}

.btn-secondary {
  @apply px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors;
}

.btn-danger {
  @apply px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors;
}

.btn-disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Warning notice */
.superuser-notice {
  @apply p-4 bg-red-900/20 border border-red-500/50 rounded-lg;
}

.superuser-notice p {
  @apply text-red-400 text-sm;
}

/* Back button */
.back-button {
  @apply p-2 bg-slate-700 hover:bg-slate-600 text-gray-300 rounded-lg transition-colors;
}

/* Header styling */
.page-header {
  @apply flex items-center justify-between mb-8;
}

.header-content {
  @apply flex items-center;
}

.header-icon {
  @apply w-16 h-16 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-6;
}

.header-title {
  @apply text-4xl font-bold text-white mb-2;
}

.header-subtitle {
  @apply text-gray-300;
}

/* Action buttons container */
.action-buttons {
  @apply flex space-x-3;
}

/* Grid layouts */
.info-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

/* Card styling */
.info-card {
  @apply bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6;
}

.card-header {
  @apply flex items-center justify-between mb-6;
}

.card-title {
  @apply text-2xl font-bold text-white;
}

.additional-info-title {
  @apply text-xl font-bold text-white mb-4;
}

/* Badge container */
.badge-container {
  @apply flex items-center space-x-4;
}

/* Form actions */
.form-actions {
  @apply mt-6 flex justify-end space-x-3;
}

/* Label styling */
.field-label {
  @apply block text-sm font-medium text-gray-300 mb-2;
}

.info-label {
  @apply block text-sm font-medium text-gray-300 mb-1;
}

/* Radio group styling */
.radio-group {
  @apply flex items-center space-x-4;
}

.radio-option {
  @apply flex items-center;
}

.radio-input {
  @apply mr-2 text-blue-600 focus:ring-blue-500;
}
