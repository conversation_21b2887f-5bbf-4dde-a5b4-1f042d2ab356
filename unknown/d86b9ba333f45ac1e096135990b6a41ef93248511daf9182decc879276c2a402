<!-- Modal Overlay -->
<div
  *ngIf="isVisible && modalConfig"
  class="fixed inset-0 z-[70] overflow-y-auto"
  (click)="onBackdropClick()">
  
  <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
    <!-- Background overlay -->
    <div class="fixed inset-0 transition-opacity bg-black bg-opacity-75 backdrop-blur-sm"></div>

    <!-- Modal panel -->
    <div 
      class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-slate-900 shadow-xl rounded-2xl border border-slate-700"
      (click)="$event.stopPropagation()">
      
      <!-- Modal Header -->
      <div class="flex items-center mb-4">
        <!-- Icon -->
        <div class="flex-shrink-0 mr-4">
          <svg 
            class="w-8 h-8" 
            [class]="getIconClass()"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24">
            <path 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              [attr.d]="getIcon()">
            </path>
          </svg>
        </div>
        
        <!-- Title -->
        <h3 class="text-lg font-semibold text-white">
          {{ modalConfig.title }}
        </h3>
      </div>

      <!-- Modal Content -->
      <div class="mb-6">
        <p class="text-gray-300 leading-relaxed">
          {{ modalConfig.message }}
        </p>
      </div>

      <!-- Modal Actions -->
      <div class="flex justify-end space-x-3">
        <!-- Cancel Button (only for confirm modals) -->
        <button
          *ngIf="modalConfig.showCancel"
          (click)="onCancel()"
          [class]="getButtonClass('cancel')">
          {{ modalConfig.cancelText || 'Отмена' }}
        </button>

        <!-- Confirm/OK Button -->
        <button
          (click)="onConfirm()"
          [class]="getButtonClass('confirm')">
          {{ modalConfig.confirmText || 'OK' }}
        </button>
      </div>
    </div>
  </div>
</div>
