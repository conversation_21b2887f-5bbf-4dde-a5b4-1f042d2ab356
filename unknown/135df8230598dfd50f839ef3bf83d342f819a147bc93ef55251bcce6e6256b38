<!-- Header Section -->
<header
  [class]="isProfilePage
    ? 'fixed top-0 left-0 right-0 z-[60] bg-slate-900/95 backdrop-blur-sm border-b border-slate-600/30'
    : 'fixed top-0 left-0 right-0 z-[60] bg-black/20 backdrop-blur-md border-b border-white/10'"
>
  <div [class]="isProfilePage
    ? 'w-full px-4 sm:px-6 lg:px-8'
    : 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'">
    <div class="flex justify-between items-center py-4">
      <!-- Logo -->
      <div class="flex items-center">
        <a href="/">
          <img
            src="/assets/icons/logo.svg"
            class="h-12 w-auto"
            alt="TOY FOR TOI"
          />
        </a>
      </div>

      <!-- Navigation -->
      <div class="flex items-center gap-6">
        <nav class="hidden md:flex space-x-8">
          <a
            (click)="navigateToGames($event)"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors text-sm font-medium cursor-pointer"
          >
            Игры
          </a>
          <a
            (click)="scrollToSection('about'); $event.preventDefault()"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors text-sm font-medium cursor-pointer"
          >
            О нас
          </a>
          <a
            (click)="scrollToSection('pricing'); $event.preventDefault()"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors text-sm font-medium cursor-pointer"
          >
            Тарифы
          </a>
          <a
            (click)="navigateToCart($event)"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors flex items-center gap-2 text-sm font-medium relative cursor-pointer"
          >
            Корзина
            <img src="/assets/icons/cart.svg" class="w-4 h-4" alt="" />
            <span *ngIf="cartItemCount > 0" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {{ cartItemCount }}
            </span>
          </a>
        </nav>

        <!-- User Menu / Login Button -->
        <div *ngIf="!isAuthenticated">
          <a
            href="/login"
            class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 text-sm font-medium shadow-lg"
          >
            Личный кабинет
          </a>
        </div>

        <!-- User Menu when authenticated - Desktop Only -->
        <div *ngIf="isAuthenticated" class="relative hidden md:block">
          <div class="flex items-center space-x-4">

            <!-- Profile Link -->
            <a
              href="/profile"
              class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 text-sm font-medium shadow-lg"
            >
              {{ currentUser?.email }}
            </a>


          </div>
        </div>

        <!-- Mobile Menu Button -->
        <button class="md:hidden text-white p-2" (click)="toggleMenu()">
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div class="md:hidden" [class.hidden]="!isMenuOpen">
      <div
        class="px-2 pt-2 pb-3 space-y-1 bg-black/40 backdrop-blur-md rounded-lg mt-2"
      >
        <!-- Profile Link - Mobile Only -->
        <a
          *ngIf="isAuthenticated"
          href="/profile"
          (click)="toggleMenu()"
          class="block px-3 py-2 text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg mb-2 font-medium transition-colors"
        >
          Личный кабинет
        </a>

        <!-- Login Link - Mobile Only (when not authenticated) -->
        <a
          *ngIf="!isAuthenticated"
          href="/login"
          (click)="toggleMenu()"
          class="block px-3 py-2 text-white bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg mb-2 font-medium transition-colors"
        >
          Личный кабинет
        </a>

        <a
          (click)="navigateToGames($event); toggleMenu()"
          class="block px-3 py-2 text-[#CFF5FF] hover:text-purple-300 transition-colors cursor-pointer"
          >Игры</a
        >
        <a
          (click)="scrollToSection('about'); toggleMenu(); $event.preventDefault()"
          class="block px-3 py-2 text-[#CFF5FF] hover:text-purple-300 transition-colors cursor-pointer"
          >О нас</a
        >
        <a
          (click)="scrollToSection('pricing'); toggleMenu(); $event.preventDefault()"
          class="block px-3 py-2 text-[#CFF5FF] hover:text-purple-300 transition-colors cursor-pointer"
          >Тарифы</a
        >
        <a
          (click)="navigateToCart($event)"
          class="flex px-3 py-2 text-[#CFF5FF] hover:text-purple-300 transition-colors items-center justify-between cursor-pointer"
        >
          Корзина
          <span *ngIf="cartItemCount > 0" class="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {{ cartItemCount }}
          </span>
        </a>
      </div>
    </div>
  </div>
</header>
